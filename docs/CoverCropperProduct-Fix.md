# CoverCropperProduct 硬编码尺寸修复说明

## 问题描述
在 `coverCropperProduct.vue` 组件中，`data()` 中的初始值还是硬编码的 300x128，与产品封面的 398x215 需求不符。

## 修复内容

### 1. 修复初始值
**修复前:**
```javascript
options: {
  autoCropWidth: 300, // 默认生成截图框宽度
  autoCropHeight: 128, // 默认生成截图框高度
}
```

**修复后:**
```javascript
options: {
  autoCropWidth: 0, // 动态计算的截图框宽度
  autoCropHeight: 0, // 动态计算的截图框高度
}
```

### 2. 添加响应式监听
```javascript
watch: {
  // 监听尺寸参数变化，动态更新裁剪框
  aspectWidth() {
    this.updateCropperOptions()
  },
  aspectHeight() {
    this.updateCropperOptions()
  },
  maxPreviewWidth() {
    this.updateCropperOptions()
  }
}
```

## 工作原理

### 动态尺寸计算
```javascript
computed: {
  cropperDimensions() {
    const aspectRatio = this.aspectWidth / this.aspectHeight
    let width = this.maxPreviewWidth
    let height = width / aspectRatio
    
    // 智能适配容器高度
    const maxHeight = 280
    if (height > maxHeight) {
      height = maxHeight
      width = height * aspectRatio
    }
    
    return {
      width: Math.round(width),
      height: Math.round(height)
    }
  }
}
```

### 自动更新机制
```javascript
methods: {
  updateCropperOptions() {
    const dimensions = this.cropperDimensions
    this.options.autoCropWidth = dimensions.width
    this.options.autoCropHeight = dimensions.height
  }
}
```

## 使用效果

### 产品封面 (398x215)
- **传入参数**: `aspectWidth: 398, aspectHeight: 215`
- **实际裁剪框**: 根据 `maxPreviewWidth: 350` 智能计算
- **计算结果**: 约 350x189 (保持 398:215 比例)
- **最终输出**: 精确的 398x215 高质量图片

### 新闻封面 (300x128)
- **使用组件**: `coverCropper.vue` (保持原有逻辑)
- **固定尺寸**: 300x128

## 验证方法

### 1. 检查初始化
```javascript
// mounted() 时会调用
this.updateCropperOptions()

// 结果: options.autoCropWidth 和 autoCropHeight 被正确设置
```

### 2. 检查动态更新
```javascript
// 当 props 变化时，watch 会触发
// 自动重新计算并更新裁剪框尺寸
```

### 3. 检查最终输出
```javascript
// uploadImg() 方法中
const resizedBlob = await this.resizeImageSmart(data, this.aspectWidth, this.aspectHeight);
// 输出精确的目标尺寸图片
```

## 优势

1. **代码一致性**: 消除硬编码，所有尺寸都通过计算获得
2. **智能适配**: 根据容器大小和比例自动计算最佳显示尺寸
3. **响应式**: 参数变化时自动更新裁剪框
4. **高质量**: 最终输出精确的目标尺寸图片

## 测试建议

1. **产品场景**: 验证输出 398x215 图片
2. **新闻场景**: 验证输出 300x128 图片
3. **参数变化**: 测试动态修改 aspectWidth/aspectHeight
4. **容器适配**: 测试不同 maxPreviewWidth 值的效果
