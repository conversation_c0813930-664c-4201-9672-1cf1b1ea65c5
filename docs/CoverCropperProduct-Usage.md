# CoverCropperProduct 组件使用说明

## 概述
`CoverCropperProduct` 是一个智能的图片裁剪上传组件，支持自定义尺寸比例，适用于各种封面图片上传场景。

## 主要优化特性

### 1. 智能尺寸适配
- 支持自定义宽高比例，不再固定尺寸
- 根据比例自动计算合适的裁剪框和预览区域尺寸
- 保持高质量图片输出

### 2. 灵活配置
- 可配置目标尺寸比例
- 可配置预览区域最大宽度
- 可配置文件名前缀

### 3. 高质量图片处理
- 使用Canvas进行高质量图片缩放
- 支持智能图片压缩，保持清晰度
- 输出指定尺寸的高质量图片

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| formDataId | String | - | 表单数据ID |
| imageUrl | String | '' | 初始图片URL |
| dialogStatus | String | '' | 对话框状态：'add' 或 'update' |
| aspectWidth | Number | 398 | 目标宽度比例 |
| aspectHeight | Number | 215 | 目标高度比例 |
| maxPreviewWidth | Number | 300 | 预览区域最大宽度 |
| fileNamePrefix | String | 'imageNews' | 文件名前缀 |

## 使用示例

### 1. 新闻封面（原始尺寸 300x128）
```vue
<cover-cropper-product
  :formDataId="newsId"
  :imageUrl="newsCoverUrl"
  :aspectWidth="300"
  :aspectHeight="128"
  :maxPreviewWidth="300"
  fileNamePrefix="newsImage"
  @getImgUrl="handleNewsImageUpload"
/>
```

### 2. 产品封面（398x215）
```vue
<cover-cropper-product
  :formDataId="productId"
  :imageUrl="productCoverUrl"
  :aspectWidth="398"
  :aspectHeight="215"
  :maxPreviewWidth="350"
  fileNamePrefix="productCover"
  @getImgUrl="handleProductImageUpload"
/>
```

### 3. 文章封面（16:9 比例）
```vue
<cover-cropper-product
  :formDataId="articleId"
  :imageUrl="articleCoverUrl"
  :aspectWidth="1600"
  :aspectHeight="900"
  :maxPreviewWidth="320"
  fileNamePrefix="articleCover"
  @getImgUrl="handleArticleImageUpload"
/>
```

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| getImgUrl | imageUrl: String | 图片上传成功后返回的图片URL |

## 方法

组件内部方法，通常不需要外部调用：

- `editCropper()`: 打开裁剪对话框
- `uploadImg()`: 上传裁剪后的图片
- `updateCropperOptions()`: 更新裁剪器配置

## 样式特性

- 响应式设计，适配不同屏幕尺寸
- 支持拖拽对话框
- 悬停效果和过渡动画
- 高质量图片预览

## 技术实现

### 智能尺寸计算
```javascript
computed: {
  cropperDimensions() {
    const aspectRatio = this.aspectWidth / this.aspectHeight
    let width = this.maxPreviewWidth
    let height = width / aspectRatio
    
    // 如果高度超过容器限制，则以高度为准重新计算
    const maxHeight = 280
    if (height > maxHeight) {
      height = maxHeight
      width = height * aspectRatio
    }
    
    return {
      width: Math.round(width),
      height: Math.round(height)
    }
  }
}
```

### 高质量图片处理
```javascript
resizeImageSmart(blob, targetWidth, targetHeight) {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    canvas.width = targetWidth
    canvas.height = targetHeight
    
    ctx.imageSmoothingEnabled = true
    ctx.imageSmoothingQuality = 'high'
    
    // 绘制并转换为高质量blob
    canvas.toBlob(resolve, blob.type, 0.9)
  })
}
```

## 注意事项

1. 确保传入的 `aspectWidth` 和 `aspectHeight` 比例合理
2. `maxPreviewWidth` 应根据页面布局合理设置
3. 上传的图片会被裁剪为精确的目标尺寸
4. 组件依赖 `vue-cropper` 库和相关上传API

## 兼容性

- Vue 2.x
- Element UI
- 现代浏览器（支持Canvas API）
