<template>
  <div class="cover-cropper-example">
    <h2>封面裁剪组件示例</h2>
    
    <!-- 业务类型选择 -->
    <el-radio-group v-model="currentDictCode" @change="handleTypeChange">
      <el-radio label="articleType">新闻类型</el-radio>
      <el-radio label="productType">产品类型</el-radio>
    </el-radio-group>
    
    <div class="example-content">
      <h3>当前配置</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="业务类型">{{ currentDictCode }}</el-descriptions-item>
        <el-descriptions-item label="使用组件">{{ coverCropperComponent }}</el-descriptions-item>
        <el-descriptions-item label="图片尺寸">{{ imageSize }}</el-descriptions-item>
        <el-descriptions-item label="文件前缀">{{ filePrefix }}</el-descriptions-item>
      </el-descriptions>
      
      <h3>封面上传</h3>
      <div class="cropper-container">
        <!-- 动态封面裁剪组件 -->
        <component
          :is="coverCropperComponent"
          :formDataId="formData.id"
          :imageUrl="formData.cover"
          :dialogStatus="dialogStatus"
          v-bind="coverCropperProps"
          @getImgUrl="handleImageUpload"
        />
      </div>
      
      <div v-if="formData.cover" class="result-preview">
        <h3>上传结果</h3>
        <img :src="formData.cover" alt="封面预览" class="preview-image" />
        <p>图片URL: {{ formData.cover }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import coverCropper from '@/views/enterpriseManagement/newsManagement/components/coverCropper.vue'
import coverCropperProduct from '@/views/enterpriseManagement/newsManagement/components/coverCropperProduct.vue'
import { genUUID } from '@/utils/th_utils.js'

export default {
  name: 'CoverCropperExample',
  components: {
    coverCropper,
    coverCropperProduct,
  },
  data() {
    return {
      currentDictCode: 'articleType',
      dialogStatus: 'create',
      formData: {
        id: genUUID(),
        cover: ''
      }
    }
  },
  computed: {
    // 判断是否为产品类型
    isProductType() {
      return this.currentDictCode === 'productType'
    },
    
    // 动态选择封面裁剪组件
    coverCropperComponent() {
      return this.isProductType ? 'coverCropperProduct' : 'coverCropper'
    },
    
    // 封面裁剪组件的动态属性
    coverCropperProps() {
      if (this.isProductType) {
        // 产品封面配置：398x215 比例
        return {
          aspectWidth: 398,
          aspectHeight: 215,
          maxPreviewWidth: 350,
          fileNamePrefix: 'productCover'
        }
      } else {
        // 新闻封面配置：使用默认的 300x128
        return {}
      }
    },
    
    // 显示当前图片尺寸
    imageSize() {
      if (this.isProductType) {
        return '398 × 215'
      } else {
        return '300 × 128'
      }
    },
    
    // 显示文件前缀
    filePrefix() {
      if (this.isProductType) {
        return 'productCover'
      } else {
        return 'imageNews'
      }
    }
  },
  methods: {
    handleTypeChange(value) {
      console.log('业务类型切换:', value)
      // 切换类型时重置封面
      this.formData.cover = ''
      // 重新生成ID
      this.formData.id = genUUID()
    },
    
    handleImageUpload(imageUrl) {
      console.log('图片上传成功:', imageUrl)
      this.formData.cover = imageUrl
      this.$message.success('封面上传成功！')
    }
  }
}
</script>

<style scoped>
.cover-cropper-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-content {
  margin-top: 20px;
}

.cropper-container {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.result-preview {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.preview-image {
  max-width: 400px;
  max-height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: block;
  margin: 10px 0;
}

h2, h3 {
  color: #303133;
  margin-bottom: 15px;
}

.el-descriptions {
  margin-bottom: 20px;
}
</style>
