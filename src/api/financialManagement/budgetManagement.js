import request from '@/utils/request'
// 预算分页查询
export function getDataListByPage(data) {
  return request({
    url: '/oa/budgetManagement/getDataListByPage',
    method: 'post',
    data,
  })
}

// 获取预算列表
export function getDataList(data) {
  return request({
    url: '/oa/budgetManagement/getDataList',
    method: 'post',
    data,
  })
}

// 获取单条预算数据
export function getData(data) {
  return request({
    url: '/oa/budgetManagement/getData',
    method: 'post',
    data,
  })
}

// 新增修改
export function saveData(data) {
  return request({
    url: '/oa/budgetManagement/saveData',
    method: 'post',
    data,
  })
}

// 删除
export function deleteData(data) {
  return request({
    url: '/oa/budgetManagement/deleteData',
    method: 'post',
    data,
  })
}

// 导出
export function exportBudgetManagementInformation(data) {
  return request({
    url: '/oa/budgetManagement/exportBudgetManagementInformation',
    method: 'post',
    responseType: 'blob',
    data,
  })
}

/**
 * 材料成本保存
 * @param data
 * @returns {*}
 */
export function materialSaveData(data) {
  return request({
    url: '/oa/BudgetMaterialCost/saveData',
    method: 'post',
    data,
  })
}

/**
 * 查询材料成本数据list
 * @param data
 * @returns {*}
 */
export function materialGetDataList(data) {
  return request({
    url: '/oa/BudgetMaterialCost/getDataList',
    method: 'post',
    data,
  })
}
/**
 * 查询材料成本数据list
 * @param data
 * @returns {*}
 */
export function materialGetInclusionStatisticsList(data) {
  return request({
    url: '/oa/BudgetMaterialCost/getInclusionStatisticsList',
    method: 'post',
    data,
  })
}
/**
 * 查询材料成本数据分页page
 * @param data
 * @returns {*}
 */
export function materialGetDataByPage(data) {
  return request({
    url: '/oa/BudgetMaterialCost/getDataListByPage',
    method: 'post',
    data,
  })
}

/**
 * 材料成本删除
 * @param data
 * @returns {*}
 */
export function deleteMaterialData(data) {
  return request({
    url: '/oa/BudgetMaterialCost/deleteData',
    method: 'post',
    data,
  })
}

/**
 * 项目成本保存
 * @param data
 * @returns {*}
 */
export function firmCostSaveData(data) {
  return request({
    url: '/oa/BudgetFirmCost/saveData',
    method: 'post',
    data,
  })
}
/**
 * 项目成本导入保存
 * @param data
 * @returns {*}
 */
export function firmCostSaveImportData(data) {
  return request({
    url: '/oa/BudgetFirmCost/importList',
    method: 'post',
    data,
  })
}
/**
 * 项目成本保存list
 * @param data
 * @returns {*}
 */
export function firmCostBathSaveData(data) {
  return request({
    url: '/oa/BudgetFirmCost/bathSaveData',
    method: 'post',
    data,
  })
}
/**
 * 查询项目成本数据分页page
 * @param data
 * @returns {*}
 */
export function firmCostGetDataByPage(data) {
  return request({
    url: '/oa/BudgetFirmCost/getDataListByPage',
    method: 'post',
    data,
  })
}
/**
 * 查询项目成本数据list
 * @param data
 * @returns {*}
 */
export function firmCostGetDataList(data) {
  return request({
    url: '/oa/BudgetFirmCost/getDataList',
    method: 'post',
    data,
  })
}
/**
 * 查询项目成本数据list
 * @param data
 * @returns {*}
 */
export function firmCostgetInclusionStatisticsList(data) {
  return request({
    url: '/oa/BudgetFirmCost/getInclusionStatisticsList',
    method: 'post',
    data,
  })
}

/**
 * 项目成本删除
 * @param data
 * @returns {*}
 */
export function deleteFirmData(data) {
  return request({
    url: '/oa/BudgetFirmCost/deleteData',
    method: 'post',
    data,
  })
}

/**
 * 预算合同类型金额保存list
 * @param data
 * @returns {*}
 */
export function budgetContractBathSaveData(data) {
  return request({
    url: '/oa/BudgetContract/bathSaveData',
    method: 'post',
    data,
  })
}
/**
 * 项目成本保存
 * @param data
 * @returns {*}
 */
export function budgetContracttSaveData(data) {
  return request({
    url: '/oa/BudgetContract/saveData',
    method: 'post',
    data,
  })
}
/**
 * 合同类型金额数据分页page
 * @param data
 * @returns {*}
 */
export function budgetContractGetDataByPage(data) {
  return request({
    url: '/oa/BudgetContract/getDataListByPage',
    method: 'post',
    data,
  })
}
/**
 * 合同类型金额数据list
 * @param data
 * @returns {*}
 */
export function budgetContractGetDataList(data) {
  return request({
    url: '/oa/BudgetContract/getDataList',
    method: 'post',
    data,
  })
}

/**
 * 合同类型金额删除
 * @param data
 * @returns {*}
 */
export function deleteBudgetContractData(data) {
  return request({
    url: '/oa/BudgetContract/deleteData',
    method: 'post',
    data,
  })
}

/**
 * 查询供应商数据list
 * @param data
 * @returns {*}
 */
export function getProviderInfoList(data) {
  return request({
    url: '/oa/providerInfo/getDataList',
    method: 'post',
    data,
  })
}
/**
 * 查询供应商分页数据page
 * @param data
 * @returns {*}
 */
export function getProviderInfoByPage(data) {
  return request({
    url: '/oa/providerInfo/getDataListByPage',
    method: 'post',
    data,
  })
}

// 预算单条查询 id: 预算id
export function getBudgetInfoIncludeDetails(id) {
  return request({
    url: `/oa/budgetManagement/getBudgetInfoIncludeDetails/${id}`,
    method: 'get',
  })
}

/**
 * 预算核查导入
 * @param data
 * @returns {*}
 */
export function budgetVerificationAdditions(data) {
  return request({
    url: '/oa/BudgetVerify/budgetVerificationAdditions',
    method: 'post',
    data,
  })
}

// 获取没有预算数据的项目列表
export function getDoBudgetProjectIdList(data) {
  return request({
    url: '/oa/budgetManagement/getDoBudgetProjectIdList',
    method: 'post',
    data,
  })
}

export function getBudgetData(data) {
  return request({
    url: '/oa/budgetManagement/getData',
    method: 'post',
    data,
  })
}
