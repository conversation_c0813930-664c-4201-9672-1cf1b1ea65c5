import request from '@/utils/request'

//校验流程定义code是否是数字开头
export function checkFlowDefCode(code) {
  return request({
    url: '/base/workflow/checkFlowDefCode/' + code,
    method: 'post',
  })
}

// 查询流程定义 分页
export function getWorkFlowByPage(data) {
  return request({
    url: '/base/workflow/getDataListByPage',
    method: 'post',
    data,
  })
}

//新增/修改流程定义
export function saveWorkFlow(data) {
  return request({
    url: '/base/workflow/saveData',
    method: 'post',
    data,
  })
}

//查询流程定义单条
export function loadWorkFlowById(data) {
  return request({
    url: '/base/workflow/getData',
    method: 'post',
    data,
  })
}

export function delWorkFlowById(id) {
  return request({
    url: '/base/workflow/delWorkFlowById/' + id,
    method: 'get',
  })
}

//部署流程
export function deployWorkFlow(data) {
  return request({
    url: '/base/workflow/deployWorkFlow',
    method: 'post',
    data,
  })
}

//校验流程是否合法
export function checkWorkFlow(data) {
  return request({
    url: '/base/workflow/checkWorkFlow',
    method: 'post',
    data,
  })
}

export function completeTask(data) {
  return request({
    url: '/base/activiti/completeTask',
    method: 'post',
    data,
  })
}

export function insertFlowMenuRef(data) {
  return request({
    url: '/base/workflow/insertFlowMenuRef',
    method: 'post',
    data,
  })
}

export function getMenuRelIdsByFlowDefId(data) {
  return request({
    url: '/base/workflow/getMenuRelIdsByFlowDefId',
    method: 'post',
    data,
  })
}

export function startProcess(data) {
  return request({
    url: '/base/activiti/startProcess',
    method: 'post',
    data,
  })
}

export function getWorkFlowDefInfoByMenuCode(data) {
  return request({
    url: '/base/workflow/getWorkFlowDefInfoByMenuCode',
    method: 'post',
    data,
  })
}

export function getFinishActivityListByBizKey(data) {
  return request({
    url: '/base/activiti/getFinishActivityListByBizKey',
    method: 'post',
    data,
  })
}

export function getActHiVariableData(data) {
  return request({
    url: '/base/activiti/getActHiVariableData',
    method: 'post',
    data,
  })
}
export function rejectTask(data) {
  return request({
    url: '/base/activiti/rejectTask',
    method: 'post',
    data,
  })
}

export function deleteWorkFlow(data) {
  return request({
    url: '/base/workflow/deleteData',
    method: 'post',
    data,
  })
}
export function resetWorkFlow(data) {
  return request({
    url: '/base/activiti/deleteWorkFlow',
    method: 'post',
    data,
  })
}
export function updateBpmnXml(data) {
  return request({
    url: '/base/workflow/updateBpmnXml',
    method: 'post',
    data,
  })
}

export function getFlowData(data) {
  return request({
    url: '/base/workflow/getFlowData',
    method: 'post',
    data,
  })
}

export function updateApprovalTime(data) {
  return request({
    url: '/base/workflow/updateApprovalTime',
    method: 'post',
    data,
  })
}

export function getAllOutTimeFlow(data) {
  return request({
    url: '/base/workflow/getAllOutTimeFlow',
    method: 'post',
    data,
  })
}

export function getTaskProcessListByBizKey(data) {
  return request({
    url: '/base/activiti/getTaskProcessListByBizKey',
    method: 'post',
    data,
  })
}

export function getPreview(data) {
  return request({
    url: '/oa/workflow/getPreview',
    method: 'post',
    data,
  })
}

export function toEnd(data) {
  return request({
    url: '/oa/workflow/toEnd',
    method: 'post',
    data,
  })
}

// 校验是否可以发起流程
export function getSysWorkflowByFlowName(data) {
  return request({
    url: '/oa/project/getSysWorkflowByFlowName',
    method: 'post',
    data,
  })
}

//获取我的抄送流程
export function getCopySendToMe(data) {
  return request({
    url: '/oa/workflow/getCopySendToMe',
    method: 'post',
    data,
  })
}

export function getToDoByPage(data) {
  return request({
    url: '/oa/workflow/getToDoByPage',
    method: 'post',
    data,
  })
}

export function getMyProcessingMetering(data) {
  return request({
    url: '/oa/workflow/getMyProcessingMetering',
    method: 'post',
    data,
  })
}

export function getMyInitiateMetering(data) {
  return request({
    url: '/oa/workflow/getMyInitiateMetering',
    method: 'post',
    data,
  })
}

export function getDynamicCodeByDeployId(data) {
  return request({
    url: '/oa/workflow/getDynamicCode',
    method: 'post',
    data,
  })
}

// 流程确认（代表已读）
export function saveWorkflowConfirmation(data) {
  return request({
    url: '/oa/workflowConfirmation/saveData',
    method: 'post',
    data,
  })
}

// 审批时获取
export function getSendCopyVars(data) {
  return request({
    url: '/oa/workflow/getSendCopyVars',
    method: 'post',
    data,
  })
}
