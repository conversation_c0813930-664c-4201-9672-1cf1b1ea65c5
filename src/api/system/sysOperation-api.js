import request from '@/utils/request'

export function getOperationLogList(data) {
  return request({
    url: '/base/operationLog/getDataList',
    method: 'post',
    data
  })
}

export function getOperationLogByPage(data) {
  return request({
    url: '/base/operationLog/getDataListByPage',
    method: 'post',
    data
  })
}

export function getOperationLog(data) {
  return request({
    url: '/base/operationLog/getData',
    method: 'post',
    data
  })
}

export function deleteOperationLog(data) {
  return request({
    url: '/base/operationLog/deleteData',
    method: 'post',
    data
  })
}

export function saveOperationLog(data) {
  return request({
    url: '/base/operationLog/saveData',
    method: 'post',
    data
  })
}

export function checkOperationLog(data) {
  return request({
    url: '/base/operationLog/checkData',
    method: 'post',
    data
  })
}


export function importOperationLog(data) {
  return request({
    url: '/base/operationLog/importList',
    method: 'post',
    data
  })
}
