import request from "@/utils/request";

/**
 * 查询微信公众平台的openid
 */
export function getOpenIdByCode(data) {
  return request({
    url: '/oa/wechat/getOpenIdByCode',
    method: 'post',
    data
  })
}


export function saveUserWechat(data) {
  return request({
    url: '/oa/wechat/manualBinding',
    method: 'post',
    data
  })
}

export function saveVoiceOfWorkers(data) {
  return request({
    url: '/oa/submitOpinions/saveData',
    method: 'post',
    data
  })
}

export function getDataListByPage(data) {
  return request({
    url: '/oa/submitOpinions/getDataListByPage',
    method: 'post',
    data,
  })
}

export function deleteData(data) {
  return request({
    url: '/oa/submitOpinions/deleteData',
    method: 'post',
    data,
  })
}


export function getAlertConfigData(data) {
  return request({
    url: '/oa/submitOpinions/getAlertConfigData',
    method: 'post',
    data,
  })
}

