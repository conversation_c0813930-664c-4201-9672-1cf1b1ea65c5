<template>
  <el-dialog
    fullscreen
    append-to-body
    :title="actionMap[dialogStatus]"
    :visible.sync="dialogFormVisible"
    @close="close"
    :close-on-click-modal="false"
    center
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      :rules="rules"
      :model="formData"
      label-position="right"
      label-width="140px"
    >
      <table class="form-table">
        <tr>
          <td colspan="3">
            <el-form-item required label="标题" prop="title">
              <el-input v-model="formData.title" style="width: 1105px" />
            </el-form-item>
          </td>
        </tr>
        <tr v-if="dictCode === 'articleType'">
          <td colspan="3">
            <el-form-item label="副标题" prop="subtitle">
              <el-input v-model="formData.subtitle" style="width: 1105px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item required label="所属分类" prop="category">
              <el-select v-model="formData.category" placeholder="请选择">
                <el-option
                  v-for="item in selectedArticleType"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td v-if="dictCode === 'articleType'">
            <el-form-item label="发布单位" prop="company">
              <el-input v-model="formData.company" style="width: 200px" />
            </el-form-item>
          </td>
          <td :colspan="dictCode === 'articleType' ? 1 : 2">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model.number="formData.sort"
                :min="0"
                :max="999"
                controls-position="right"
                style="width: 200px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item required label="封面" prop="cover">
              <!--                  <el-upload-->
              <!--                    class="avatar-uploader"-->
              <!--                    action=""-->
              <!--                    :show-file-list="false"-->
              <!--                    :http-request="uploadFile">-->
              <!--                    <img v-if="formData.cover" :src="formData.cover" class="avatar">-->
              <!--                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>-->
              <!--                  </el-upload>-->
              <!-- 动态选择封面裁剪组件 -->
              <component
                :is="coverCropperComponent"
                :formDataId="formData.id"
                :imageUrl="formData.cover"
                :dialogStatus="dialogStatus"
                v-bind="coverCropperProps"
                @getImgUrl="getImgUrl"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="描述" prop="remark">
              <el-input
                type="textarea"
                v-model="formData.remark"
                :rows="4"
                style="width: 1105px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="内容" prop="content">
              <!-- <VueEditor
                  class="articleEditor"
                  style="width:1105px"
                  use-custom-image-handler
                  @image-added="handleImageAdded"
                  :editorToolbar="customToolbar"
                  v-model="formData.content"
                ></VueEditor> -->
              <!-- 工具栏 -->
              <div v-if="dialogFormVisible">
                <Toolbar
                  style="border-bottom: 1px solid #ccc; width: 1600px"
                  :editor="editor"
                  :defaultConfig="toolbarConfig"
                />
                <!-- 编辑器 -->
                <Editor
                  style="height: 800px; overflow-y: hidden; width: 1600px"
                  :defaultConfig="editorConfig"
                  v-model="formData.content"
                  @onChange="onChange"
                  @onCreated="onCreated"
                />
              </div>
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { saveData } from '@/api/newCms/newcmsArticle'
  import { getDictList } from '@/api/system/dict-api'
  import { genUUID } from '@/utils/th_utils.js'
  import { multiUpload as uploadFile } from '@/api/system/uploadFile-api'
  import { fileAddr } from '@/utils/constants'
  import { VueEditor } from 'vue2-editor'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
  import coverCropper from './coverCropper.vue'
  import coverCropperProduct from './coverCropperProduct.vue'
  export default {
    name: 'articleEdit',
    components: {
      VueEditor,
      Editor,
      Toolbar,
      coverCropper,
      coverCropperProduct,
    },
    props: {
      dictCode: {
        type: String,
        default: 'articleType',
      },
    },
    data() {
      return {
        fileAddr: fileAddr,
        dialogFormVisible: false,
        formLoading: false,
        formData: {},
        actionMap: {
          update: this.dictCode === 'articleType' ? '编辑新闻' : '编辑产品',
          create: this.dictCode === 'articleType' ? '新增新闻' : '新增产品',
        },
        dialogStatus: '',
        selectedArticleType: [],
        rules: {
          title: [{ required: true, message: '标题为必填', trigger: 'blur' }],
          category: [
            { required: true, message: '所属分类为必填', trigger: 'change' },
          ],
          cover: [{ required: true, message: '封面为必填', trigger: 'change' }],
        },
        // customToolbar: [
        //   ["bold", "italic", "underline"],
        //   [{ align: "" }, { align: "center" }, { align: "right" }],
        //   [{ list: "ordered" }, { list: "bullet" }, { list: "check" }],
        //   [{ background: [] }, { color: [] }],
        //   ["image", "link"],
        //   ["strike"],
        //   ["clean"]
        // ],
        editor: null,
        toolbarConfig: {
          // toolbarKeys: [ /* 显示哪些菜单，如何排序、分组 */ ],
          // excludeKeys: [ /* 隐藏哪些菜单 */ ],
        },
        editorConfig: {
          placeholder: '请输入内容...',
          // autoFocus: false,

          // 所有的菜单配置，都要在 MENU_CONF 属性下
          MENU_CONF: {
            uploadImage: {
              async customUpload(file, insertFn) {
                // JS 语法
                // file 即选中的文件
                // 自己实现上传，并得到图片 url alt href
                // 最后插入图片
                const params = new FormData()
                params.append('file', file)
                params.append('bizCode', 'newsImage')
                params.append('bizId', 'newsImage')
                uploadFile(params).then((response) => {
                  if (response.result.length > 0) {
                    var imgstr = response.result[0].urlPath
                    insertFn(imgstr, 'image', imgstr)
                    this.$notify({
                      title: '信息',
                      message: '上传成功！',
                      type: 'success',
                      duration: 2000,
                    })
                  }
                })
              },
            },
            uploadVideo: {
              async customUpload(file, insertFn) {
                // TS 语法
                // file 即选中的文件
                // 自己实现上传，并得到视频 url poster
                // 最后插入视频
                const params = new FormData()
                params.append('file', file)
                params.append('bizCode', 'newsVideo')
                params.append('bizId', 'newsVideo')
                uploadFile(params).then((response) => {
                  if (response.result.length > 0) {
                    var videostr = response.result[0].urlPath
                    insertFn(videostr)
                    this.$notify({
                      title: '信息',
                      message: '上传成功！',
                      type: 'success',
                      duration: 2000,
                    })
                  }
                })
              },
            },
          },
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
      }),
      // 判断是否为产品类型
      isProductType() {
        return this.dictCode === 'productType'
      },
      // 动态选择封面裁剪组件
      coverCropperComponent() {
        return this.isProductType ? 'coverCropperProduct' : 'coverCropper'
      },
      // 封面裁剪组件的动态属性
      coverCropperProps() {
        if (this.isProductType) {
          // 产品封面配置：398x215 比例
          return {
            aspectWidth: 398,
            aspectHeight: 215,
            maxPreviewWidth: 350,
            fileNamePrefix: 'productCover'
          }
        } else {
          // 新闻封面配置：使用默认的 300x128
          return {}
        }
      }
    },
    async created() {
      this.getDictDetails()
    },
    beforeDestroy() {
      const editor = this.editor
      if (editor == null) return
      editor.destroy() // 组件销毁时，及时销毁 editor ，重要！！！
    },
    methods: {
      onCreated(editor) {
        this.editor = Object.seal(editor) // 【注意】一定要用 Object.seal() 否则会报错
      },
      onChange(editor) {
        // console.log('onChange', editor.getHtml()) // onChange 时获取编辑器最新内容
      },
      getDictDetails() {
        getDictList({ dictCode: this.dictCode }).then((response) => {
          this.selectedArticleType = response.result[0].children
        })
      },
      uploadFile(params) {
        const uparams = new FormData()
        uparams.append('file', params.file)
        uparams.append('bizCode', this.$route.name)
        uparams.append('bizId', this.formData.id)
        uploadFile(uparams).then((response) => {
          if (response.result.length > 0) {
            this.formData.cover = response.result[0].urlPath
            this.$notify({
              title: '信息',
              message: '上传成功！',
              type: 'success',
              duration: 2000,
            })
          }
        })
      },
      getImgUrl(url) {
        this.formData.cover = url
      },
      async handleImageAdded(file, Editor, cursorLocation, resetUploader) {
        const params = new FormData()
        params.append('file', file)
        params.append('bizCode', this.$route.name)
        params.append('bizId', this.formData.id)
        uploadFile(params).then((response) => {
          if (response.result.length > 0) {
            var imgstr = response.result[0].urlPath
            Editor.insertEmbed(cursorLocation, 'image', imgstr)
            this.$notify({
              title: '信息',
              message: '上传成功！',
              type: 'success',
              duration: 2000,
            })
          }
        })
      },
      showEdit(row) {
        if (!row) {
          this.dialogStatus = 'create'
          this.initForm()
        } else {
          this.dialogStatus = 'update'
          this.formData = { ...row, isAdd: false }
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          title: '',
          subtitle: '',
          company: '',
          category: '',
          cover: '',
          remark: '',
          content: '',
          sort: '1',
          isAdd: true,
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            let data = { ...this.formData }
            saveData(data)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
  .avatar-uploader ::v-deep {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .el-upload:hover {
      border-color: #409eff;
    }
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
  .articleEditor ::v-deep .ql-editor {
    height: 500px;
  }
</style>
