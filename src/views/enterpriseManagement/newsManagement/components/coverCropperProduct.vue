<template>
  <div>
    <div class="user-info-head" :style="uploadAreaStyle" @click="editCropper()">
      <img
        v-if="options.img !== ''"
        class="img-circle img-lg"
        :src="options.img"
        :style="uploadAreaStyle"
        title="点击上传产品封面"
      />
      <i
        v-else
        class="el-icon-plus avatar-uploader-icon"
        :style="{ lineHeight: uploadAreaStyle.height }"
      ></i>
    </div>
    <el-dialog
      v-drag
      append-to-body
      :title="title"
      :visible.sync="open"
      width="960px"
      @close="closeDialog()"
      @opened="modalOpened"
    >
      <el-row>
        <el-col :md="12" :style="{ height: '350px' }" :xs="24">
          <vue-cropper
            v-if="visible"
            ref="cropper"
            :auto-crop="options.autoCrop"
            :auto-crop-height="options.autoCropHeight"
            :auto-crop-width="options.autoCropWidth"
            :fixed-box="options.fixedBox"
            :img="options.img"
            :info="true"
            :original="options.original"
            @realTime="realTime"
          />
        </el-col>
        <el-col :md="12" :style="{ height: '350px' }" :xs="24">
          <div class="avatar-upload-preview" :style="previewStyle">
            <img :src="previews.url" :style="previews.img" />
          </div>
        </el-col>
      </el-row>
      <br />
      <el-row>
        <el-col :lg="2" :md="2">
          <el-upload
            action="#"
            :before-upload="beforeUpload"
            :http-request="requestUpload"
            :show-file-list="false"
          >
            <el-button size="small">
              选择
              <i class="el-icon-upload el-icon--right"></i>
            </el-button>
          </el-upload>
        </el-col>
        <el-col :lg="{ span: 1, offset: 2 }" :md="2">
          <el-button icon="el-icon-plus" size="small" @click="changeScale(1)" />
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :md="2">
          <el-button
            icon="el-icon-minus"
            size="small"
            @click="changeScale(-1)"
          />
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :md="2">
          <el-button
            icon="el-icon-refresh-left"
            size="small"
            @click="rotateLeft()"
          />
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :md="2">
          <el-button
            icon="el-icon-refresh-right"
            size="small"
            @click="rotateRight()"
          />
        </el-col>
        <el-col :lg="{ span: 2, offset: 6 }" :md="2">
          <el-button size="small" type="primary" @click="uploadImg()">
            提 交
          </el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
  import store from '@/store'
  import { VueCropper } from 'vue-cropper'
  // import { uploadAvatar } from "@/api/system/user";
  import { uploadAvatar } from '@/api/user'
  import { multiUpload as uploadFile } from '@/api/system/uploadFile-api'
  import { string } from 'mockjs/src/mock/random/basic'

  export default {
    components: { VueCropper },
    props: {
      formDataId: {
        type: String,
      },
      imageUrl: {
        type: String,
        default: '',
      },
      // 当前弹窗状态：新增\编辑
      dialogStatus: {
        type: String,
        default: '',
      },
      // 目标宽度比例
      aspectWidth: {
        type: Number,
        default: 398,
      },
      // 目标高度比例
      aspectHeight: {
        type: Number,
        default: 215,
      },
      // 预览区域最大宽度
      maxPreviewWidth: {
        type: Number,
        default: 300,
      },
      // 文件名前缀
      fileNamePrefix: {
        type: String,
        default: 'imageNews',
      },
    },
    data() {
      return {
        // 是否显示弹出层
        open: false,
        // 是否显示cropper
        visible: false,
        // 弹出层标题
        title: '裁剪封面',
        options: {
          img: '', //裁剪图片的地址
          autoCrop: true, // 是否默认生成截图框
          autoCropWidth: 0, // 动态计算的截图框宽度
          autoCropHeight: 0, // 动态计算的截图框高度
          fixedBox: true, // 固定截图框大小 不允许改变
          // original: false, // 上传图片按照原始比例渲染
        },
        previews: {},
        // 成功提交标志
        uploadSuccess: false,
      }
    },
    computed: {
      // 计算裁剪框尺寸
      cropperDimensions() {
        const aspectRatio = this.aspectWidth / this.aspectHeight
        let width = this.maxPreviewWidth
        let height = width / aspectRatio

        // 如果高度超过容器限制，则以高度为准重新计算
        const maxHeight = 320 // 容器最大高度，适配398x215尺寸
        if (height > maxHeight) {
          height = maxHeight
          width = height * aspectRatio
        }

        return {
          width: Math.round(width),
          height: Math.round(height),
        }
      },
      // 预览区域样式
      previewStyle() {
        return {
          width: `${this.cropperDimensions.width}px`,
          height: `${this.cropperDimensions.height}px`,
        }
      },
      // 上传区域样式
      uploadAreaStyle() {
        return {
          width: `${this.cropperDimensions.width}px`,
          height: `${this.cropperDimensions.height}px`,
        }
      },
    },
    watch: {
      imageUrl: {
        handler(newVaule) {
          this.options.img = newVaule
        },
      },
    },
    watch: {
      // 监听尺寸参数变化，动态更新裁剪框
      aspectWidth() {
        this.updateCropperOptions()
      },
      aspectHeight() {
        this.updateCropperOptions()
      },
      maxPreviewWidth() {
        this.updateCropperOptions()
      },
    },
    mounted() {
      this.options.img = this.imageUrl
      this.updateCropperOptions()
    },
    methods: {
      // 更新裁剪器配置
      updateCropperOptions() {
        const dimensions = this.cropperDimensions
        this.options.autoCropWidth = dimensions.width
        this.options.autoCropHeight = dimensions.height
      },
      // 编辑头像
      editCropper() {
        this.updateCropperOptions()
        this.open = true
        this.visible = true
      },
      // 打开弹出层结束时的回调
      modalOpened() {
        this.visible = true
      },
      // 覆盖默认的上传行为
      requestUpload() {},
      // 向左旋转
      rotateLeft() {
        this.$refs.cropper.rotateLeft()
      },
      // 向右旋转
      rotateRight() {
        this.$refs.cropper.rotateRight()
      },
      // 图片缩放
      changeScale(num) {
        num = num || 1
        this.$refs.cropper.changeScale(num)
      },
      // 上传预处理
      beforeUpload(file) {
        if (file.type.indexOf('image/') === -1) {
          this.$baseMessage(
            '文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。',
            'error',
            'vab-hey-message-error'
          )
        } else {
          const reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = () => {
            this.options.img = reader.result
          }
        }
      },
      // 上传图片
      uploadImg() {
        this.$refs.cropper.getCropBlob(async (data) => {
          // 保持裁剪后的原始尺寸，不进行缩放
          // 这样可以保留高分辨率，只是按比例裁剪

          const fileType = data.type || 'image/jpeg'
          const fileExtension = fileType.split('/')[1]
          const fileName = `${this.fileNamePrefix}.${fileExtension}`

          const uparams = new FormData()
          uparams.append(
            'file',
            new File([data], fileName, { type: data.type })
          )
          uparams.append('bizCode', this.$route.name)
          uparams.append('bizId', this.formDataId)
          uploadFile(uparams).then((response) => {
            if (response.result.length > 0) {
              this.$notify({
                title: '信息',
                message: '上传成功！',
                type: 'success',
                duration: 2000,
              })
              this.uploadSuccess = true
              this.$set(this.options, 'img', response.result[0].urlPath)
              this.$emit('getImgUrl', response.result[0].urlPath)
              this.open = false
              this.visible = false
            }
          })
        })
      },
      // 智能调整图片大小
      resizeImageSmart(blob, targetWidth, targetHeight) {
        return new Promise((resolve) => {
          const img = new Image()
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')

          img.onload = () => {
            // 设置画布尺寸为目标尺寸
            canvas.width = targetWidth
            canvas.height = targetHeight

            // 保持高质量缩放
            ctx.imageSmoothingEnabled = true
            ctx.imageSmoothingQuality = 'high'

            // 绘制图片到画布
            ctx.drawImage(img, 0, 0, targetWidth, targetHeight)

            // 转换为blob，使用高质量设置
            canvas.toBlob(
              (resizedBlob) => {
                resolve(resizedBlob)
              },
              blob.type,
              0.9
            ) // 0.9 质量参数，保持高质量
          }

          img.src = URL.createObjectURL(blob)
        })
      },
      // 调整图片大小（保留原方法作为备用）
      resizeImage(blob, width, height) {
        return new Promise((resolve) => {
          const img = new Image()
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')

          img.onload = () => {
            // 计算纵横比
            const aspectRatio = img.width / img.height
            let targetWidth, targetHeight

            // 保持纵横比
            if (width / height > aspectRatio) {
              targetWidth = height * aspectRatio
              targetHeight = height
            } else {
              targetWidth = width
              targetHeight = width / aspectRatio
            }

            // 设置画布尺寸
            canvas.width = targetWidth
            canvas.height = targetHeight

            // 保持高质量缩放
            ctx.imageSmoothingEnabled = true
            ctx.imageSmoothingQuality = 'high'

            // Draw the image
            ctx.drawImage(img, 0, 0, targetWidth, targetHeight)

            // Convert to blob
            canvas.toBlob((resizedBlob) => {
              resolve(resizedBlob)
            }, blob.type)
          }

          img.src = URL.createObjectURL(blob)
        })
      },
      // 实时预览
      realTime(data) {
        this.previews = data
      },
      // 取消截图，关闭对话框
      closeDialog() {
        if (!this.uploadSuccess && this.dialogStatus !== 'update') {
          // 没有上传成功且不是编辑操作则重置
          this.options.img = ''
        }
        this.open = false
        this.visible = false
        this.uploadSuccess = false
      },
    },
  }
</script>
<style scoped lang="scss">
  .user-info-head {
    position: relative;
    text-align: center;
    margin: 0 auto;
    overflow: hidden;
    margin-bottom: 20px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
      border-color: #409eff;
    }

    &::after {
      content: '点击上传';
      position: absolute;
      text-align: center;
      left: 0;
      right: 0;
      height: 32px;
      width: 100%;
      bottom: 0;
      color: #eee;
      background: rgba(13, 90, 255, 0.5);
      font-size: 14px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      cursor: pointer;
      line-height: 32px;
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100%;
      height: 100%;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .avatar-upload-preview {
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    margin-left: 40px;
    box-shadow: 0 0 4px #ccc;
    overflow: hidden;
    border-radius: 4px;
  }

  /* image */
  .img-circle {
    border-radius: 4px;
  }

  .img-lg {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
</style>
