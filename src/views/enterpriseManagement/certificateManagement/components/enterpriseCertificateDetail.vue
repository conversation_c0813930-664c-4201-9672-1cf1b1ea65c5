<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    title="证书详情"
    :visible.sync="dialogDetailVisible"
    width="1550px"
    v-drag
    center
    top="5vh"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-descriptions
          class="margin-top"
          :column="2"
          border
          size="medium"
          v-loading="formLoading"
          title="企业资质证书"
        >
          <el-descriptions-item>
            <template slot="label">资质所属单位</template>
            {{ formData.certificateOwnershipUnitName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">资质管理部门</template>
            {{ formData.departName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">证书名称</template>
            {{ formData.documentTypeName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">证书编号</template>
            {{ formData.documentCode }}
          </el-descriptions-item>
              <el-descriptions-item>
            <template slot="label">发证机关</template>
            {{ formData.issuingAuthorityName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">颁证日期</template>
            {{ formData.recordDate | formateTime }}
          </el-descriptions-item>
          
           
          <el-descriptions-item >
            <template slot="label">有效开始时间</template>
            {{ formData.effectiveStartTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">有效截止时间</template>
            <!-- {{ formData.effectiveEndTime | dateformat('YYYY-MM-DD') }} -->
            {{ formData.effectiveEndTime ? dateFormat(formData.effectiveEndTime) : '永久' }}
          </el-descriptions-item>
          <!-- <el-descriptions-item>
            <template slot="label">证书状态</template>
            {{ formData.documentStatus }}
          </el-descriptions-item> -->
        
           <el-descriptions-item :span="2">
            <template slot="label">备注</template>
            {{ formData.remark }}
          </el-descriptions-item>
        </el-descriptions>
        <!-- 附件上传 -->
        <!-- <UploadLargeFileFdfsPopup
          ref="UploadLargeFileFdfsPopup"
        ></UploadLargeFileFdfsPopup> -->
        <UploadLargeFileFdfsPopupDetail ref="UploadLargeFileFdfsPopupDetail" />
        <!-- 审批 -->
        <!-- <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        /> -->
      </div>
      <!-- <div v-if="formData.approvalResult !== '0'"> -->
        <!-- 审批记录 -->
        <!-- <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div> -->
    </div>
    <div slot="footer" class="dialog-footer" v-if="!isApproval">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { parseTime } from '@/utils' // 详情
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  export default {
    props: {},
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
     filters: {
      formateTime(val) {
        if (!val) return ''
        return val.substr(0, 11)
      },
    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        formLoading: false,
        isApproval: false,
      }
    },
    methods: {
      showApprovalFlowByParams(bizKey, row, params) {
        this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(bizKey, row, params)
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      dateFormat(date) {
        if (!date) {
          return ''
        }
        return parseTime(new Date(date), '{y}-{m}-{d}')
      },
      showDialog(row) {
        this.isApproval = false
        this.formData = { ...row } 
        this.$nextTick(() => {
          // if (this.formData.approvalResult !== '0') {
          //   this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
          //     this.formData.id
          //   )
          // }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
            isShow: false,
          })
        })
        this.dialogDetailVisible = true
      },
    },
  }
</script>
<style lang="scss" scoped>
  .margin-two {
    padding-top: 10px;
  }
</style>
