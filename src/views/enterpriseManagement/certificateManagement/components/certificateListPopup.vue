<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    :title="title"
    top="2vh"
    :visible.sync="dialogFormVisible"
    width="1200px"
    @closed="close"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item
            v-if="certificateType === 'enterprise'"
            label="证书名称"
            label-width="110px"
            prop="documentName"
          >
            <el-input
              v-model="queryForm.documentTypeName"
              clearable
              placeholder="输入证书名称"
            />
          </el-form-item>
          <el-form-item
            v-else
            label="证书名称"
            label-width="110px"
            prop="documentName"
          >
            <el-input
              v-model="queryForm.documentByName"
              clearable
              placeholder="输入证书名称"
            />
          </el-form-item>
          <el-form-item
            label="证件编号"
            label-width="110px"
            prop="documentCode"
          >
            <el-input
              v-model="queryForm.documentCode"
              clearable
              placeholder="输入证件编号"
            />
          </el-form-item>
          <el-form-item label="使用情况" prop="usageSituation">
            <el-select
              ref="selectUsageSituation"
              v-model="queryForm.usageSituation"
              filterable
              placeholder="请选择证书状态"
              style="width: 250px"
            >
              <el-option
                v-for="item in usageSituationList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
    </vab-query-form>
    <el-table
      ref="tableDataRef"
      v-loading="listLoading"
      border
      :data="list"
      :height="460"
      row-key="id"
      stripe
      @select="handleRowSelect"
      @select-all="selectAll"
    >
      <el-table-column
        align="center"
        label="选择"
        :reserve-selection="true"
        :selectable="selectableFun"
        type="selection"
        width="55"
      />
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '使用状态'">
            <el-tag
              :type="
                row.certificateStstus === 0
                  ? 'success'
                  : row.certificateStstus === 1
                  ? 'warning'
                  : ''
              "
            >
              {{ getStatuseName(row) }}
            </el-tag>
          </span>
          <span v-else-if="item.label === '证书状态'">
            <el-tag
              :type="
                row.documentStatus === '有效'
                  ? 'success'
                  : row.documentStatus === '无效'
                  ? 'warning'
                  : ''
              "
            >
              {{ row.documentStatus }}
            </el-tag>
          </span>
          <span v-else-if="item.label === '证件有效截止日期'">
            {{
              row.effectiveEndTime ? dateFormat(row.effectiveEndTime) : '永久'
            }}
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :page-sizes="[5, 10, 20]"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <div class="select-content">
      <div class="select-title">当前已选择:</div>
      <div class="select-list">
        <el-tag
          v-for="item in selectRows"
          :key="item.id"
          closable
          @close="closeTag(item)"
        >
          {{ item.documentByName }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogFormVisible = false">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { getDataListByPage as getEnterpriseCertificateDataListByPage } from '@/api/certificateManagement/enterpriseCertificate'
  import { getDataListByPage as getPersonnelCertificateDataListByPage } from '@/api/certificateManagement/personnelCertificate'
  import { parseTime } from '@/utils' // 详情
  export default {
    data() {
      return {
        title: '选择证件列表',
        listLoading: false,
        dialogFormVisible: false,
        queryForm: {
          documentCode: '',
          usageSituation: '',
          documentTypeName: '',
          documentByName: '',
        },
        layout: 'total, sizes, prev, pager, next, jumper',
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 0,
        },
        selectRows: [],
        certificateType: '',
        usageSituationList: [
          { id: 'effective', name: '未占用' },
          { id: 'inVain', name: '已占用' },
        ],
        columns: [
          {
            label: '证书名称',
            prop: 'documentByName',
            disableCheck: true,
          },
          {
            label: '发证机关',
            prop: 'issuingAuthorityName',
          },
          {
            label: '证件持有人',
            prop: 'personnelByName',
          },
          {
            label: '证书状态',
            prop: 'documentStatus',
          },
          {
            label: '证件编号',
            prop: 'documentCode',
            width: '200',
          },
          {
            label: '证件有效截止日期',
            prop: 'effectiveEndTime',
          },
          {
            label: '使用状态',
            prop: 'certificateStstus',
          },
        ],
        list: [],
        selectIds: [],
        isMulti: false,
      }
    },
    computed: {},
    created() {},
    methods: {
      showDialog(certificateType) {
        this.certificateType = certificateType
        this.queryForm = {
          ...this.queryForm,
        }
        this.fetchData()
        this.dialogFormVisible = true
        if (this.selectIds.length > 0) {
          this.$nextTick(() => {
            this.$refs.tableDataRef.store.states.selection = [...this.selectIds]
          })
        }
      },
      getStatuseName(row) {
        this.selectableFun(row)
        if (row.certificateStstus === 1) {
          return '已占用'
        } else {
          return '未占用'
        }
      },
      selectAll(selection) {
        if (!this.isMulti) {
          this.$refs.tableDataRef.clearSelection()
          this.selectRows = []
        } else {
          this.selectRows = selection
        }
      },
      // 限制勾选
      selectableFun(row) {
        if (row.certificateStstus === 1 || row.documentStatus === '无效') {
          return false
        } else {
          return true
        }
      },
      handleRowClick(row) {
        const idx = this.selectRows.findIndex((item) => item.id === row.id)
        if (this.isMulti) {
          if (idx >= 0) {
            this.selectRows.splice(idx, 1)
            this.$refs.tableDataRef.toggleRowSelection(row, false)
          } else {
            this.selectRows.push(row)
            this.$refs.tableDataRef.toggleRowSelection(row, true)
          }
        } else {
          this.$refs.tableDataRef.clearSelection()
          if (idx >= 0) {
            this.selectRows = []
          } else {
            this.$refs.tableDataRef.toggleRowSelection(row, true)
            this.selectRows = [row]
          }
        }
      },
      handleRowSelect(selection, row) {
        if (this.isMulti) {
          this.selectRows = selection
        } else {
          this.$refs.tableDataRef.clearSelection()
          if (this.selectRows.length > 0 && selection.length === 0) {
            this.selectRows = []
          } else {
            this.$refs.tableDataRef.toggleRowSelection(row, true)
            this.selectRows = [row]
          }
        }
      },
      dateFormat(date) {
        if (date == '永久') {
          return '永久'
        }
        return parseTime(new Date(date), '{y}-{m}-{d}')
      },
      closeTag(row) {
        const idx = this.selectRows.findIndex((item) => item.id === row.id)
        this.selectRows.splice(idx, 1)
        const { selection } = this.$refs.tableDataRef.store.states
        this.$refs.tableDataRef.store.states.selection = selection.filter(
          (item) => item.id !== row.id
        )
      },
      save() {
        let data = []
        if (this.selectRows && this.selectRows.length) {
          data = data.concat(this.selectRows)
        }
        this.$emit('callBackData', data)
        this.dialogFormVisible = false
      },
      close() {
        this.dialogFormVisible = false
        this.$refs.tableDataRef.clearSelection()
        this.selectRows = []
        this.selectIds = []
        this.pageInfo.curPage = 1
        this.$refs.form.resetFields()
      },
      async fetchData() {
        this.queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        this.listLoading = true
        if (this.certificateType === 'enterprise') {
          const {
            result: { records, total },
          } = await getEnterpriseCertificateDataListByPage(this.queryForm)
          this.list = records
          this.pageInfo.total = Number(total)
          this.listLoading = false
        } else if (this.certificateType === 'personnel') {
          const {
            result: { records, total },
          } = await getPersonnelCertificateDataListByPage(this.queryForm)
          this.list = records
          this.pageInfo.total = Number(total)
          this.listLoading = false
        }
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.fetchData()
      },
      handleQuery() {
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      resetForm(formName) {
        this.pageInfo.curPage = 1
        this.$refs[formName].resetFields()
        this.fetchData()
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-table__header .el-checkbox {
    display: none;
  }
  .select-content {
    margin-top: 10px;
    .select-list {
      margin-top: 10px;
    }
  }
</style>
