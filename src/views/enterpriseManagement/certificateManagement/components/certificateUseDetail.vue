<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="titleName"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1550px"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-descriptions
          v-loading="formLoading"
          border
          class="margin-top"
          :column="2"
          size="medium"
          :title="titleName"
        >
          <el-descriptions-item>
            <template slot="label">证书所属单位或人员</template>
            {{ formData.personnel }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">使用部门</template>
            {{ formData.certificateDepartName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">证书名称</template>
            {{ formData.certificateName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">证书编号</template>
            {{ formData.certificateCode }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">证件形式</template>
            {{ getCertificateForm(formData.certificateForm) }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">开始日期</template>
            {{ formData.startTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">结束日期</template>
            {{ formData.endTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <!-- <el-descriptions-item>
            <template slot="label">证书状态</template>
            {{ formData.documentStatus }}
          </el-descriptions-item> -->

          <el-descriptions-item :span="2">
            <template slot="label">备注</template>
            {{ formData.remark }}
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template slot="label">使用事由</template>
            {{ formData.affair }}
          </el-descriptions-item>
        </el-descriptions>
        <!-- 附件上传 -->
        <!-- <UploadLargeFileFdfsPopup
          ref="UploadLargeFileFdfsPopup"
        ></UploadLargeFileFdfsPopup> -->
        <UploadLargeFileFdfsPopupDetail ref="UploadLargeFileFdfsPopupDetail" />
        <!-- 审批 -->
        <!-- <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        /> -->
      </div>
      <!-- <div v-if="formData.approvalResult !== '0'"> -->
      <!-- 审批记录 -->
      <!-- <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div> -->
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    filters: {
      formateTime(val) {
        if (!val) return ''
        return val.substr(0, 11)
      },
    },
    props: {},
    data() {
      return {
        dialogDetailVisible: false,
        titleName: '',
        formData: {},
        formLoading: false,
        isApproval: false,
      }
    },
    methods: {
      showApprovalFlowByParams(bizKey, row, params) {
        this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(bizKey, row, params)
        })
      },
      getCertificateForm(certificateForm) {
        if (certificateForm == 'originalDocument') {
          return '原件'
        } else {
          return '扫描件'
        }
      },
      getCertificateTypeName(certificateType) {
        console.log(certificateType)
        if (certificateType == 'enterprise') {
          this.titleName = '企业证书使用详情'
        } else {
          this.titleName = '个人证书使用详情'
        }
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      showDialog(row) {
        this.isApproval = false
        this.formData = { ...row }
        this.getCertificateTypeName(this.formData.certificateType)
        this.$nextTick(() => {
          // if (this.formData.approvalResult !== '0') {
          //   this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
          //     this.formData.id
          //   )
          // }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
            isShow: false,
          })
        })
        this.dialogDetailVisible = true
      },
    },
  }
</script>
<style lang="scss" scoped>
  .margin-two {
    padding-top: 10px;
  }
</style>
