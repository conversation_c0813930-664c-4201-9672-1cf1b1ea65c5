<template>
  <el-dialog
    v-drag
    append-to-body
    center
    class="dialog-container"
    :close-on-click-modal="false"
    title="工资条管理详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1400px"
    @close="close"
  >
    <div
      style="
        /*display: flex;*/
        max-height: 75vh;
        overflow-y: scroll;
        overflow-x: hidden;
      "
    >
      <div style="flex: 1">
        <el-tabs
          v-model="activeName"
          v-loading="formLoading"
          type="card"
          @tab-click="handleTabClick"
        >
          <el-tab-pane label="基本信息" name="first">
            <el-descriptions
              border
              class="margin-top"
              :column="3"
              size="medium"
              style="margin-bottom: 20px"
            >
              <el-descriptions-item>
                <template slot="label">单号</template>
                {{ formData.serialNumber }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label">登记人</template>
                {{ formData.createByName }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label">登记部门</template>
                {{ formData.recordDepartName }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label">标题</template>
                {{ formData.title }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label">登记日期</template>
                {{ formData.recordDate | dateformat('YYYY-MM-DD') }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label">发薪月份</template>
                {{ formData.payMonth | dateformat('YYYY-MM') }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label">备注</template>
                {{ formData.remark }}
              </el-descriptions-item>
            </el-descriptions>
            <!-- 附件上传 -->
            <UploadLargeFileFdfsPopupDetail
              ref="UploadLargeFileFdfsPopupDetail"
              style="margin-top: 20px"
            />
          </el-tab-pane>

          <el-tab-pane label="工资单" name="second">
            <vab-query-form>
              <vab-query-form-top-panel>
                <el-form
                  ref="form"
                  :inline="true"
                  label-width="60px"
                  :model="queryForm"
                  @submit.native.prevent
                >
                  <el-form-item label="姓名" prop="userName">
                    <el-input
                      v-model="queryForm.userName"
                      clearable
                      placeholder="请输入姓名"
                      style="width: 200px"
                    />
                  </el-form-item>
                  <el-form-item label="手机" prop="telephone">
                    <el-input
                      v-model="queryForm.telephone"
                      clearable
                      placeholder="请输入手机号"
                      style="width: 200px"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      icon="el-icon-search"
                      native-type="submit"
                      type="primary"
                      @click="handleQuery"
                    >
                      查询
                    </el-button>
                    <el-button
                      icon="el-icon-refresh-right"
                      @click.native="resetForm('form')"
                    >
                      重置
                    </el-button>
                  </el-form-item>
                </el-form>
              </vab-query-form-top-panel>
            </vab-query-form>
            <el-table
              ref="tableSort"
              v-loading="listLoading"
              border
              :data="dateList"
              max-height="500"
              stripe
            >
              <el-table-column
                v-for="(item, index) in finallyColumns"
                :key="index"
                :align="item.center ? item.center : 'center'"
                :fixed="item.prop === 'userName'"
                :label="item.label"
                :prop="item.prop"
                :sortable="item.sortable ? item.sortable : false"
                :width="item.width ? item.width : 'auto'"
              >
                <template #default="{ row }">
                  <span
                    v-if="item.prop === 'userName' || item.prop === 'telephone'"
                  >
                    {{ row[item.prop] }}
                  </span>
                  <span v-else>
                    {{ row[item.prop] | currencyFormat }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              :current-page="pageInfo.curPage"
              :layout="layout"
              :page-size="pageInfo.pageSize"
              :page-sizes="[5, 10, 20]"
              :total="pageInfo.total"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="close">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  import { getPayslipByPage } from '@/api/financialManagement/payslipManagement-api'
  import VabQueryForm from '@/vab/components/VabQueryForm/index.vue'
  import VabQueryFormTopPanel from '@/vab/components/VabQueryForm/components/VabQueryFormTopPanel.vue'
  import tableMix from '@/views/mixins/baseTable'

  export default {
    name: 'PayslipManagementDetail',
    components: {
      VabQueryFormTopPanel,
      VabQueryForm,
      UploadLargeFileFdfsPopupDetail,
    },
    mixins: [tableMix],
    props: {},
    data() {
      return {
        activeName: 'first',
        formLoading: false,
        listLoading: true,
        dateList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 0,
        },
        queryForm: {
          bizId: '',
          userName: '',
          telephone: '',
        },
        dialogDetailVisible: false,
        formData: {},
        isApproval: false,
        checkList: [],
        columns: [
          {
            label: '姓名',
            prop: 'userName',
            width: '80',
          },
          {
            label: '电话',
            prop: 'telephone',
            width: '120',
          },
          {
            label: '基本工资',
            prop: 'basicSalary',
            width: '110',
          },
          {
            label: '绩效工资',
            prop: 'performanceSalary',
            width: '110',
          },
          {
            label: '岗位工资',
            prop: 'positionSalary',
            width: '110',
          },
          {
            label: '保密津贴',
            prop: 'confidentialityAllowance',
            width: '110',
          },
          {
            label: '全勤工资',
            prop: 'fullAttendanceSalary',
            width: '110',
          },
          {
            label: '工资合计',
            prop: 'totalSalary',
            width: '110',
          },
          {
            label: '流量费',
            prop: 'trafficFee',
            width: '110',
          },
          {
            label: '偏远地区/风沙',
            prop: 'remoteAreaAllowance',
            width: '110',
          },
          {
            label: '工地津贴',
            prop: 'constructionAllowance',
            width: '110',
          },
          {
            label: '生日',
            prop: 'birthdayAllowance',
            width: '110',
          },
          {
            label: '出差',
            prop: 'businessTripAllowance',
            width: '110',
          },
          {
            label: '电脑',
            prop: 'computerAllowance',
            width: '110',
          },
          {
            label: '兼职司机',
            prop: 'partTimeDriver',
            width: '110',
          },
          {
            label: '餐补',
            prop: 'mealAllowance',
            width: '110',
          },
          {
            label: '车辆补贴',
            prop: 'vehicleSubsidy',
            width: '110',
          },
          {
            label: '绩效工资',
            prop: 'otherPerformanceSalary',
            width: '110',
          },
          {
            label: '其他',
            prop: 'other',
            width: '110',
          },
          {
            label: '其他小计',
            prop: 'totalAllowance',
            width: '110',
          },
          {
            label: '绩效扣款',
            prop: 'performanceDeduction',
            width: '110',
          },
          {
            label: '其他扣款',
            prop: 'otherDeductions',
            width: '110',
          },
          {
            label: '扣款',
            prop: 'deductions',
            width: '110',
          },
          {
            label: '工资总额',
            prop: 'grossSalary',
            width: '110',
          },
          {
            label: '养老保险',
            prop: 'pensionInsurance',
            width: '110',
          },
          {
            label: '医疗保险',
            prop: 'medicalInsurance',
            width: '110',
          },
          {
            label: '失业保险',
            prop: 'unemploymentInsurance',
            width: '110',
          },
          {
            label: '住房公积金',
            prop: 'housingFund',
            width: '110',
          },
          {
            label: '社保合计',
            prop: 'totalSocialSecurity',
            width: '110',
          },
          {
            label: '代扣个税',
            prop: 'withheldTax',
            width: '110',
          },
          {
            label: '已发',
            prop: 'paidAmount',
            width: '110',
          },
          {
            label: '实发工资',
            prop: 'actualSalary',
            width: '110',
          },
        ],
      }
    },
    methods: {
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      async showDialog(row) {
        this.activeName = 'first'
        this.isApproval = false
        this.formData = { ...row }
        this.queryForm.bizId = this.formData.id
        this.dialogDetailVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: 'payslipManagement',
            isShow: true,
          })
        })
      },
      handleTabClick(tab) {
        switch (tab.name) {
          case 'first':
            break
          case 'second':
            this.fetchData()
            break
        }
      },
      async fetchData() {
        //async 异步数据处理方法
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const { result } = await getPayslipByPage(this.queryForm)
        this.dateList = result.records //把请求到的数据赋值给表格
        this.pageInfo.total = Number(result.total)
        this.listLoading = false
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.fetchData()
      },
      handleQuery() {
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      resetFormPage() {
        this.resetForm('form')
      },
      resetForm(formName) {
        this.pageInfo.curPage = 1
        this.$refs[formName].resetFields()
        this.queryForm.id = ''
        this.fetchData()
      },
      close() {
        this.formData = this.$options.data().formData
        this.dateList = []
        this.activeName = 'first'
        this.pageInfo.curPage = 1
        this.$refs['form'].resetFields()
        this.dialogDetailVisible = false
      },
    },
  }
</script>
<style lang="scss" scoped>
  .dialog-container {
    ::v-deep .el-pagination {
      position: relative;
      bottom: 0;
    }
  }
</style>
