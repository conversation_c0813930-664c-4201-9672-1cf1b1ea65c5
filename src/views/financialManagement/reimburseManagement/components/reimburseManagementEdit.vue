<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="actionMap[dialogStatus]"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1420px"
    @close="close"
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-tabs
        v-model="activeName"
        v-loading="formLoading"
        :before-leave="beforeLeave"
        type="card"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="基本信息" name="first">
          <el-form
            ref="dataForm"
            v-loading="formLoading"
            :inline="true"
            label-position="right"
            label-width="140px"
            :model="formData"
            :rules="rules"
          >
            <table class="form-table">
              <tr>
                <td>
                  <el-form-item label="单号" prop="serialNumber">
                    <el-input
                      v-model="formData.serialNumber"
                      disabled
                      placeholder="自动生成"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="申请人" prop="createBy">
                    <el-input v-model="formData.createByName" disabled />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="申请部门" prop="createDepart">
                    <el-input v-model="formData.createDepartName" disabled />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="申请日期" prop="recordDate">
                    <el-date-picker
                      v-model="formData.recordDate"
                      :disabled="!formData.isAdd"
                      placeholder="选择预计开始时间"
                      style="width: 250px"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </td>
                <td v-show="type === 'depart'">
                  <el-form-item label="费用所属部门" prop="depart">
                    <t-form-tree
                      v-model="formData.depart"
                      :default-props="{
                        children: 'children',
                        label: 'departName',
                      }"
                      :disabled="!formData.isAdd"
                      placeholder="请选择费用所属部门"
                      :show-top="false"
                      :tree-data="departTreeData"
                      @change="changeDepart"
                    />
                  </el-form-item>
                </td>
                <td v-show="type === 'project'">
                  <el-form-item label="费用所属部门" prop="departName">
                    <el-input
                      v-model="formData.departName"
                      disabled
                      placeholder="选择项目自动导入"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="所属项目" prop="projectName">
                    <el-input
                      v-model="formData.projectName"
                      :disabled="!formData.isAdd"
                      placeholder="选择所属项目"
                      style="width: 250px"
                      @click.native="projectChange"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="报销人" prop="reimbursementApplicant">
                    <t-form-user
                      v-model="formData.reimbursementApplicant"
                      :disabled="!formData.isAdd"
                      placeholder="请选择"
                      @change="changeReimbursementApplicant"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="报销类型" prop="reimbursementType">
                    <el-select
                      v-model="formData.reimbursementType"
                      :disabled="!formData.isAdd"
                      multiple
                      placeholder="请选择"
                      @change="changeReimbursementType"
                    >
                      <el-option
                        v-for="item in reimbursementTypeList"
                        :key="item.value"
                        :disabled="item.disabled"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="付款单位" prop="payerId">
                    <t-form-tree
                      v-model="formData.payerId"
                      :default-props="{
                        children: 'children',
                        label: 'departFullName',
                      }"
                      :show-top="false"
                      :tree-data="payeeIdDepartTreeData"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item prop="reimbursementAmount">
                    <template #label>
                      报销金额（元）
                      <el-tooltip
                        class="item"
                        :content="`报销金额为${
                          formData.isBudget === 0 ? '报销费用项' : '报销预算单'
                        }中的累计金额`"
                        effect="dark"
                        placement="top"
                      >
                        <i class="el-icon-question" style="color: #ffba00"></i>
                      </el-tooltip>
                    </template>
                    <el-input
                      v-model="formData.reimbursementAmount"
                      disabled
                      placeholder=""
                    />
                  </el-form-item>
                </td>
                <td v-show="type === 'depart'">
                  <el-form-item label="是否有申请单" prop="isApply">
                    <el-radio-group
                      v-model="formData.isApply"
                      :disabled="!formData.isAdd"
                      @input="isApplyChange"
                    >
                      <el-radio :label="1">是</el-radio>
                      <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </td>
                <td v-show="type === 'project'">
                  <el-form-item prop="isBudget">
                    <template #label>
                      是否预算
                      <el-tooltip
                        class="item"
                        content="该报销是否属于项目的预算范围"
                        effect="dark"
                        placement="top"
                      >
                        <i class="el-icon-question" style="color: #ffba00"></i>
                      </el-tooltip>
                    </template>
                    <el-radio
                      v-model="formData.isBudget"
                      :disabled="!formData.isAdd"
                      :label="1"
                    >
                      是
                    </el-radio>
                    <el-radio
                      v-model="formData.isBudget"
                      :disabled="!formData.isAdd"
                      :label="0"
                    >
                      否
                    </el-radio>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="项目单号" prop="projectSerialNumber">
                    <el-input
                      v-model="formData.projectSerialNumber"
                      disabled
                      placeholder="选择项目自动导入"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="收款人" prop="payeeDetailIds">
                    <el-button
                      icon="el-icon-plus"
                      type="primary"
                      @click="showPayeeDetailDialog"
                    >
                      选择收款信息
                    </el-button>
                    <div
                      v-if="formData.payeeDetailRow.length > 0"
                      style="
                        width: 1000px;
                        display: flex;
                        justify-content: flex-start;
                        flex-wrap: wrap;
                      "
                    >
                      <span
                        v-for="(item, index) in formData.payeeDetailRow"
                        :key="index"
                      >
                        {{
                          item
                            ? item.userName +
                              '(' +
                              item.bankDeposit +
                              ':' +
                              item.cardNumber +
                              ')'
                            : ''
                        }}
                        <span
                          style="color: #f00; cursor: pointer"
                          @click="clearPayeeDetail(index)"
                        >
                          <i class="el-icon-delete"></i>
                        </span>
                        <span>&nbsp;&nbsp;</span>
                      </span>
                    </div>
                  </el-form-item>
                </td>
              </tr>

              <tr v-if="formData.isApply === 1">
                <td colspan="3">
                  <el-form-item label="关联申请单" prop="applyIds">
                    <el-button
                      icon="el-icon-plus"
                      type="primary"
                      @click="showApplyFormSelect"
                    >
                      选择申请单
                    </el-button>
                    <div
                      v-if="formData.applyIdRow.length > 0"
                      style="
                        width: 1000px;
                        display: flex;
                        justify-content: flex-start;
                        flex-wrap: wrap;
                      "
                    >
                      <span
                        v-for="(item, index) in formData.applyIdRow"
                        :key="index"
                      >
                        <el-link
                          v-if="item !== null"
                          type="primary"
                          @click="showDetails(item)"
                        >
                          {{ item.applyNo }}
                        </el-link>
                        <span
                          v-if="item !== null && formData.isAdd"
                          style="color: #f00; cursor: pointer"
                          @click="clearApplyForm(index)"
                        >
                          <i class="el-icon-delete"></i>
                        </span>
                        <span v-if="!formData.isAdd">&nbsp;&nbsp;</span>
                      </span>
                    </div>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="报销事由" prop="reimbursementReason">
                    <el-input
                      v-model="formData.reimbursementReason"
                      maxlength="1000"
                      placeholder="请输入内容"
                      style="width: 1170px"
                      type="textarea"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="formData.remark"
                      maxlength="1000"
                      placeholder="请输入内容"
                      style="width: 1170px"
                      type="textarea"
                    />
                  </el-form-item>
                </td>
              </tr>
            </table>
            <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          :label="formData.isBudget === 0 ? '报销费用项' : '报销预算单'"
          name="second"
        >
          <!--          报销预算单-->
          <reimburse-list-project-edit
            v-if="formData.isBudget === 1"
            ref="reimburseListProjectEdit"
            :budget-list="budgetList"
            @checkIsWarning="checkIsWarning"
            @closeDialog="closeDialog"
          />
          <!--          报销费用项-->
          <reimburse-list-depart-edit
            v-if="formData.isBudget === 0"
            ref="reimburseListDepartEdit"
            :budget-list="budgetList"
            @checkIsWarningCosts="checkIsWarningCosts"
            @closeDialog="closeDialog"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button :loading="saveLoading" type="primary" @click="saveAndContinue">
        {{ activeName === 'second' ? '确认' : '保存并继续' }}
      </el-button>
    </template>
    <!--    收款信息-->
    <payeeDetailDialog
      ref="payeeDetailDialog"
      :multiple="true"
      @confirm="confirmPayeeDetail"
    />
    <!--    申请单-->
    <applyFormSelect ref="applyFormSelect" @confirm="confirmApplyForm" />
    <!--      采购申请详情-->
    <purchase-application-detail
      v-if="showPurchaseApplicationDetail"
      ref="purchaseApplicationDetail"
    />
    <!--      出差申请详情-->
    <travel-detail v-if="showTravelDetail" ref="travelDetail" />
    <!--      招待申请详情-->
    <entertain-application-detail
      v-if="showEntertainApplicationDetail"
      ref="entertainApplicationDetail"
    />
    <!--  调休请假详情  -->
    <leave-detail v-if="showLeaveDetail" ref="leaveDetail" />
    <!--  油卡充值详情  -->
    <fuel-card-recharge-detail
      v-if="showFuelCardRechargeDetail"
      ref="fuelCardRechargeDetail"
    />
    <!--  短时外出  -->
    <temporary-outing-detail
      v-if="showTemporaryOutingDetail"
      ref="temporaryOutingDetail"
    />
    <!-- 公车申请 -->
    <official-car-delivery-detail
      v-if="showOfficialCarDeliveryDetail"
      ref="officialCarDeliveryDetail"
    />
    <!--项目车辆维保-->
    <project-vehicle-maintenance-detail
      v-if="showProjectVehicleMaintenanceDetail"
      ref="projectVehicleMaintenanceDetail"
    />
    <!--机关车辆维保-->
    <depart-vehicle-maintenance-detail
      v-if="showDepartVehicleMaintenanceDetail"
      ref="departVehicleMaintenanceDetail"
    />
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import moment from 'moment'
  import {
    getPayeeAndApplyData,
    saveData,
  } from '@/api/financialManagement/reimburseManagement'
  import { genUUID } from '@/utils/th_utils.js'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import applyFormSelect from '../../components/applyFormSelect.vue'
  import payeeDetailDialog from '../../components/payeeDetailDialog.vue'
  // 报销费用单
  import reimburseListDepartEdit from '@/views/financialManagement/reimburseManagement/components/reimburseListDepartEdit.vue'
  // 报销预算单
  import reimburseListProjectEdit from '@/views/financialManagement/reimburseManagement/components/reimburseListProjectEdit.vue'
  // 采购详情组件
  import { getPurchaseData } from '@/api/administrativeManagement/purchaseApplication-api'
  import PurchaseApplicationDetail from '@/views/approvalManagement/purchasingManagement/components/purchaseApplicationDetail.vue'
  // 出差详情组件
  import { getDataEvection } from '@/api/administrativeManagement/evection'
  import TravelDetail from '@/views/approvalManagement/travelManagement/components/travelDepartDetail.vue'
  // 招待详情组件
  import { getDataEntertainApplication } from '@/api/administrativeManagement/entertainApplication-api'
  import EntertainApplicationDetail from '@/views/approvalManagement/entertainManagement/components/entertainApplicationDetail.vue'
  // 调休请假详情
  import { getData as getDataLeave } from '@/api/administrativeManagement/compensatoryLeave-api'
  import LeaveDetail from '@/views/approvalManagement/components/leaveDetail.vue'
  // 油卡充值详情
  import { getDataFuelCardRecharge } from '@/api/administrativeManagement/fuelCardRecharge-api'
  import FuelCardRechargeDetail from '@/views/approvalManagement/components/fuelCardRechargeDetail.vue'
  // 短时外出
  import { getData as getDataTemporaryOuting } from '@/api/approvalManagement/temporaryOuting-api'
  import TemporaryOutingDetail from '@/views/approvalManagement/components/temporaryOutingDetail.vue'
  // 公车申请
  import { getData as getDataOfficialCarDelivery } from '@/api/approvalManagement/busApplication-api'
  import OfficialCarDeliveryDetail from '@/views/approvalManagement/components/busApplicationDetail.vue'
  // 项目车辆维保
  import { getData as getDataVehicleMaintenance } from '@/api/approvalManagement/vehicleMaintenance-api'
  import projectVehicleMaintenanceDetail from '@/views/approvalManagement/vehicleMaintenance/components/projectVehicleMaintenanceDetail.vue'
  // 机关车辆维保
  import departVehicleMaintenanceDetail from '@/views/approvalManagement/vehicleMaintenance/components/departVehicleMaintenanceDetail.vue'

  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'
  import { constantsExpose } from '@/utils/constantsExpose'
  import DepartVehicleMaintenanceDetail from '@/views/approvalManagement/vehicleMaintenance/components/departVehicleMaintenanceDetail.vue'

  export default {
    name: 'ReimburseManagementEdit',
    components: {
      DepartVehicleMaintenanceDetail,
      ProjectListPopup,
      reimburseListProjectEdit,
      reimburseListDepartEdit,
      payeeDetailDialog,
      UploadLargeFileFdfsPopup,
      applyFormSelect,
      PurchaseApplicationDetail,
      TravelDetail,
      EntertainApplicationDetail,
      LeaveDetail,
      FuelCardRechargeDetail,
      TemporaryOutingDetail,
      OfficialCarDeliveryDetail,
      projectVehicleMaintenanceDetail,
      departVehicleMaintenanceDetail,
    },
    props: {
      // 成本费用项
      budgetList: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      type(newValue) {
        // 根据 type 的新值更新 projectName 的 required 属性
        this.rules.projectName[0].required = newValue === 'project';
      },
    },
    data() {
      return {
        // 按钮
        saveLoading: false,
        dialogFormVisible: false,
        activeName: 'first',
        reimburseManagementId: '',
        formLoading: false,
        formData: {
          payeeDetailRow: [],
          applyIdRow: [],
        },
        actionMap: {
          update: '编辑报销',
          create: '新增报销',
        },
        oldSelectProject: null,
        // 报销类型
        reimbursementTypeList: [],
        commonTypeList: [
          {
            value: 'purchase',
            label: '采购申请',
            disabled: false,
          },
          {
            value: 'evection',
            label: '出差申请',
            disabled: false,
          },
          {
            value: 'entertain',
            label: '招待申请',
            disabled: false,
          },
          {
            value: 'leave',
            label: '调休请假',
            disabled: false,
          },
          {
            value: 'vehicleMaintenance',
            label: '车辆维保',
            disabled: false,
          },
          {
            value: 'fuelCardRecharge',
            label: '油卡充值',
            disabled: false,
          },
        ],
        // 项目报销类型
        projectReimbursementTypeList: [

        ],
        // 机关报销类型
        departReimbursementTypeList: [
          {
            value: 'temporaryOuting',
            label: '短时外出',
            disabled: false,
          },
          {
            value: 'officialCarDelivery',
            label: '公车申请',
            disabled: false,
          },
          {
            value: 'other',
            label: '其它',
            disabled: true,
          },
        ],
        dialogStatus: '',
        type: 'project',
        listLoading: false,
        projectStatusList:[],
        rules: {
          recordDate: [
            {
              required: true,
              message: '报销申请日期为必填',
              trigger: 'change',
            },
          ],
          projectName: [
            { required: true, message: '所属项目为必填', trigger: 'change' },
          ],
          reimbursementApplicant: [
            { required: true, message: '报销人为必填', trigger: 'change' },
          ],
          reimbursementType: [
            { required: true, message: '报销类型为必填', trigger: 'change' },
          ],
          reimbursementReason: [
            { required: true, message: '报销事由为必填', trigger: 'blur' },
          ],
          payerId: [
            { required: true, message: '付款单位为必选', trigger: 'change' },
          ],
          depart: [
            {
              required: true,
              message: '费用所属部门为必填',
              trigger: 'change',
            },
          ],
          payeeDetailIds: [
            { required: true, message: '收款人为必选', trigger: 'change' },
          ],
          applyIds: [
            { required: true, message: '申请单为必选', trigger: 'change' },
          ],
        },
        // 报销预算单、报销费用项是否校验通过
        isWarning: false,
        // 采购申请详情
        showPurchaseApplicationDetail: false,
        // 出差申请详情
        showTravelDetail: false,
        // 招待申请详情
        showEntertainApplicationDetail: false,
        // 调休请假详情
        showLeaveDetail: false,
        // 油卡充值详情
        showFuelCardRechargeDetail: false,
        // 短时外出
        showTemporaryOutingDetail: false,
        // 公车申请
        showOfficialCarDeliveryDetail: false,
        // 项目车辆维保
        showProjectVehicleMaintenanceDetail: false,
        // 机关车辆维保
        showDepartVehicleMaintenanceDetail: false,
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'user/departList',
        departTreeData: 'acl/allDepartList',
      }),
      payeeIdDepartTreeData() {
        return this.departTreeData.filter((departInfo) => {
          return departInfo.departType.indexOf('2') === -1
        })
      },
    },
    created() {},
    methods: {
      changeReimbursementType() {
        this.formData.applyIdRow = []
        this.formData.applyIds = []
      },
      // 切换标签之前的钩子
      beforeLeave() {
        if (this.reimburseManagementId === '' && this.formData.isAdd === true) {
          this.$message({
            message: '请先保存报销基本信息',
            type: 'warning',
          })
          return false
        } else {
          return true
        }
      },
      // 切换tab页面
      handleTabClick(tab) {
        switch (tab.name) {
          case 'first':
            if (typeof this.formData.reimbursementType == 'string') {
              // 保存时将reimbursementType转变为字符串，如果切换到基本信息页面，转为数组
              this.formData.reimbursementType =
                this.formData.reimbursementType.split(',')
            }
            break
          case 'second':
            if (this.formData.isBudget === 1) {
              // 预算内报销
              this.$refs.reimburseListProjectEdit.showEdit(
                this.reimburseManagementId,
                this.type,
                this.formData.projectId,
                this.formData.reimbursementApplicant
              )
            } else {
              // 预算外报销
              this.$refs.reimburseListDepartEdit.showEdit(
                this.reimburseManagementId,
                this.type,
                this.formData.isApply
              )
            }
            break
        }
      },
      // 保存下一步
      saveAndContinue() {
        switch (this.activeName) {
          case 'first':
            this.save()
            break
          case 'second':
            if (this.isWarning) {
              this.$message({
                message: '金额填写有误',
                type: 'error',
              })
              return
            }
            this.saveLoading = true
            // 延时500ms关闭弹窗，避免数据还没保存就关闭了
            setTimeout(() => {
              this.closeDialog()
              this.saveLoading = false
            }, 500)
            break
        }
      },
      // 报销预算单金额填写有误
      checkIsWarning(val) {
        this.isWarning = val
      },
      // 报销费用项金额填写有误
      checkIsWarningCosts(val) {
        this.isWarning = val
      },
      // 保存后关闭弹窗
      closeDialog() {
        this.close()
      },
      // 点击选中项目
      projectChange() {
        // 2ddc8e7b-4691-43b7-8260-13d435e3cee6 跟踪
        // 348ac237-1cb0-4b44-85fa-961bfb4465e9 结算
        // 80042b9f-72bd-4249-b321-48f2dfe4db2b 运维
        // c2302242-8143-4ad6-876f-1d38785e1f44 实施

        if(this.type === 'depart'){
          this.projectStatusList = ['2ddc8e7b-4691-43b7-8260-13d435e3cee6']
        }else{
          this.projectStatusList = ['348ac237-1cb0-4b44-85fa-961bfb4465e9','80042b9f-72bd-4249-b321-48f2dfe4db2b','c2302242-8143-4ad6-876f-1d38785e1f44']
        }
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName,
          queryParams:  { projectStatusList: this.projectStatusList }
        })
      },
      // 获取选中的项目信息
      getProjectInfo(data) {
        if (data.length > 0) {
          const project = data[0]
          if (this.type === 'depart') {
            if(project.createDepart !== undefined){
              this.oldSelectProject = project
              this.formData.projectSerialNumber = project.serialNumber
              this.formData.projectId = project.id
              this.formData.projectName = project.projectName
            }
            if( this.formData.depart !== this.oldSelectProject.createDepart){
              this.$confirm('费用所属部门与选择项目费用部门不一致，是否重置费用所属部门？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                // 用户点击确定，重置费用所属部门
                this.formData.depart = this.oldSelectProject.createDepart
                this.formData.departCode = this.oldSelectProject.createDepartCode
                this.formData.departName = this.oldSelectProject.createDepartName
              }).catch(() => {
                // 用户点击取消，不做处理
              });
            }
          }else{
            this.formData.projectId = project.id
            this.formData.projectName = project.projectName
            this.formData.projectSerialNumber = project.serialNumber
            this.formData.depart = project.depart
            this.formData.departCode = project.departCode
            this.formData.departName = project.departName
            this.formData.applyIdRow = []
            this.formData.applyIds = []
          }


        }
      },
      // 选择申请单
      showApplyFormSelect() {
        if (
          this.formData.type === 'project' &&
          this.formData.projectId === ''
        ) {
          this.$baseMessage('请选择项目', 'error', 'vab-hey-message-error')
          return
        } else if (
          this.formData.type === 'depart' &&
          this.formData.depart === ''
        ) {
          this.$baseMessage(
            '请选择费用所属部门',
            'error',
            'vab-hey-message-error'
          )
          return
        } else if (this.formData.reimbursementApplicant === '') {
          this.$baseMessage('请选择报销人', 'error', 'vab-hey-message-error')
          return
        } else if (this.formData.reimbursementType.length === 0) {
          this.$baseMessage('请选择报销类型', 'error', 'vab-hey-message-error')
          return
        }
        const projectId = this.type === 'project' ? this.formData.projectId : ''
        const depart = this.type === 'depart' ? this.formData.depart : ''
        const reimburseId =
          this.formData.isAdd === false ? this.formData.id : ''
        const params = {
          applyIds: this.formData.applyIds,
          reimbursementApplicant: this.formData.reimbursementApplicant,
          projectId: projectId,
          depart: depart,
          type: this.type,
          applyTypeList: this.formData.reimbursementType,
          reimburseId: reimburseId,
        }
        this.$refs.applyFormSelect.showDialog(params)
      },
      // 切换是否存在申请单
      isApplyChange() {
        // 临时存放报销类型
        const reimbursementTypeList = this.formData.reimbursementType
        if (
          this.formData.isApply === 0 &&
          this.formData.applyIdRow.length > 0
        ) {
          // 当前存在申请单，但切换为没有申请单的状态：则清空申请单
          this.$confirm('当前已选择申请单，是否清空？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              this.formData.applyIdRow = []
              this.formData.applyIds = []
            })
            .catch(() => {
              this.formData.isApply = 1
              this.formData.reimbursementType = reimbursementTypeList
            })
        }
        // 无申请单，默认选"其它"
        if (this.formData.isApply === 0) {
          this.reimbursementTypeList.map((item) => {
            // item.disabled = item.value !== 'other'
            item.disabled = false
          })
          this.formData.reimbursementType = ['other']
        } else {
          // 有申请单，不可选"其它"
          this.reimbursementTypeList.map((item) => {
            item.disabled = item.value === 'other'
          })
          this.formData.reimbursementType = []
        }
      },
      // 选择收款信息
      showPayeeDetailDialog() {
        this.$refs.payeeDetailDialog.showDialog(this.formData.payeeDetailRow)
      },
      // 确认申请单
      confirmApplyForm(row) {
        this.formData.applyIds = []
        this.formData.applyIdRow = row
        row.forEach((item) => {
          this.formData.applyIds.push(item.id)
        })
        // 使用 this.$refs.form.validateField 方法校验字段
        this.$refs.dataForm.validateField('applyIds', () => {})
      },
      // 确认收款信息
      confirmPayeeDetail(row) {
        this.formData.payeeDetailIds = []
        this.formData.payeeDetailRow = row
        row.forEach((item) => {
          this.formData.payeeDetailIds.push(item.id)
        })
        // 使用 this.$refs.form.validateField 方法校验字段
        this.$refs.dataForm.validateField('payeeDetailIds', () => {})
      },
      clearPayeeDetail(id) {
        this.formData.payeeDetailIds.splice(id, 1)
        this.formData.payeeDetailRow.splice(id, 1)
      },
      // 改变费用所属部门
      changeDepart(depart) {
        for (const item of this.departTreeData) {
          if (item.id === depart) {
            this.formData.departCode = item.departCode
            this.formData.departName = item.departName
          }
        }
        this.formData.applyIdRow = []
        this.formData.applyIds = []
      },
      // 改变报销人
      changeReimbursementApplicant() {
        this.formData.applyIdRow = []
        this.formData.applyIds = []
      },
      clearApplyForm(id) {
        this.formData.applyIds.splice(id, 1)
        this.formData.applyIdRow.splice(id, 1)
      },
      async showEdit(type, row) {
        this.dialogFormVisible = true
        // 重置isWarning为false,防止上一条数据中的isWarning的值影响现在这条数据的保存
        this.isWarning = false
        // 正常来讲报销类型应该存放到字典里的，结果字典编码重复了，如果修改字典编码后端也需要改，所以先这样吧
        if (type === 'project') {
          this.reimbursementTypeList = this.commonTypeList.concat(
            this.projectReimbursementTypeList
          )
        } else if (type === 'depart') {
          this.reimbursementTypeList = this.commonTypeList.concat(
            this.departReimbursementTypeList
          )
        }
        // 获取项目list
        if (!row) {
          this.dialogStatus = 'create'
          this.reimburseManagementId = ''
          // 保存报销类型：项目\机关
          this.type = type
          this.initForm(type)
          // 新增时默认报销类型："其它"不可选
          this.reimbursementTypeList.map((item) => {
            item.disabled = item.value === 'other'
          })
        } else {
          this.formLoading = true
          this.dialogStatus = 'update'
          this.formData = {
            ...row,
            payeeDetailIds: [],
            applyIds: [],
            applyIdRow: [],
            payeeDetailRow: [],
            isAdd: false,
          }
          // 编辑时，重置报销类型参数的disabled值
          this.reimbursementTypeList.map((item) => {
            item.disabled = false
          })
          this.formData.reimbursementType =
            this.formData.reimbursementType.split(',')
          this.reimburseManagementId = this.formData.id
          this.type = this.formData.type
          await this.getPayeeAndApplyData(this.formData.id)
          this.formLoading = false
        }
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'reimburseManagement',
          })
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 获取当前报销信息的收款人、清单
      async getPayeeAndApplyData(id) {
        await getPayeeAndApplyData({ reimburseId: id }).then((res) => {
          this.formData.payeeDetailRow = res.result.payeeDetailIds
          this.formData.applyIdRow = res.result.applyIds

          this.formData.payeeDetailIds = []
          this.formData.payeeDetailRow.forEach((item) => {
            if (item) {
              this.formData.payeeDetailIds.push(item.id)
            }
          })
          this.formData.applyIds = []
          this.formData.applyIdRow.forEach((item) => {
            if (item) {
              this.formData.applyIds.push(item.id)
            }
          })
        })
      },
      // 初始化表单
      initForm(type) {
        this.formData = {
          id: genUUID(),
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          recordDate: moment().format('YYYY-MM-DD'),
          projectId: '',
          projectName: '',
          projectSerialNumber: '',
          reimbursementReason: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          depart: '',
          departCode: '',
          departName: '',
          payerId: '',
          isBudget: type === 'project' ? 1 : 0, // 是否存在预算 1预算内  0预算外
          isApply: 1, // 是否存在申请单
          reimbursementApplicant: this.userId,
          payeeDetailIds: [],
          payeeDetailRow: [],
          reimbursementAmount: 0,
          remark: '',
          type: type,
          operationCode: type === 'project' ? 'XMBX' : 'JGBX',
          isAdd: true,
          applyIds: [],
          applyIdRow: [],
          reimbursementType: '',
          serialNumberTable: 'reimburse_management',
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            // 保存时将reimbursementType转变为字符串
            this.formData.reimbursementType =
              this.formData.reimbursementType.join(',')
            // 项目报销时，选择项目不保存部门id值depart
            if (this.formData.type === 'project') {
              this.formData.depart = ''
            }
            let data = { ...this.formData }
            saveData(data)
              .then(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '保存基本信息成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.formData.isAdd = false
                // 保存成功进入下一步：报销费用单\报销预算单
                this.activeName = 'second'
                this.reimburseManagementId = this.formData.id
                if (this.formData.isBudget === 1) {
                  this.$refs.reimburseListProjectEdit.showEdit(
                    this.reimburseManagementId,
                    this.type,
                    this.formData.projectId,
                    this.formData.reimbursementApplicant
                  )
                } else {
                  this.$refs.reimburseListDepartEdit.showEdit(
                    this.reimburseManagementId,
                    this.type,
                    this.formData.isApply
                  )
                }
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd
                    ? '新增失败!' + err.message
                    : '修改失败!' + err.message,
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$emit('fetch-data')
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
        this.activeName = 'first'
      },
      // 打开关联申请单详情
      showDetails(item) {
        switch (item.applyType) {
          // 采购
          case 'purchase':
            this.getPurchaseData(item.id)
            break
          // 出差
          case 'evection':
            this.getEvectionData(item.id)
            break
          // 招待
          case 'entertain':
            this.getEntertainData(item.id)
            break
          // 调休请假
          case 'leave':
            this.getLeaveData(item.id)
            break
          // 油卡充值
          case 'fuelCardRecharge':
            this.getFuelCardRechargeData(item.id)
            break
          // 短时外出
          case 'temporaryOuting':
            this.getTemporaryOutingData(item.id)
            break
          // 公车申请
          case 'officialCarDelivery':
            this.getOfficialCarDeliveryData(item.id)
            break
          // 车辆维保
          case 'vehicle_maintenance':
            this.getVehicleMaintenanceData(item.id)
            break
        }
      },
      // 获取采购申请单数据、打开详情页
      async getPurchaseData(id) {
        this.showPurchaseApplicationDetail = true
        await getPurchaseData({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.purchaseApplicationDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      // 获取出差申请单数据、打开详情页
      async getEvectionData(id) {
        this.showTravelDetail = true
        await getDataEvection({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.travelDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      // 获取招待申请单数据、打开详情页
      async getEntertainData(id) {
        this.showEntertainApplicationDetail = true
        await getDataEntertainApplication({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.entertainApplicationDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      // 获取调休请假申请单数据、打开详情页
      async getLeaveData(id) {
        this.showLeaveDetail = true
        await getDataLeave({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.leaveDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      //   获取油卡充值申请单数据、打开详情页
      async getFuelCardRechargeData(id) {
        this.showFuelCardRechargeDetail = true
        await getDataFuelCardRecharge({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.fuelCardRechargeDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      //   短时外出申请单数据、打开详情页
      async getTemporaryOutingData(id) {
        this.showTemporaryOutingDetail = true
        await getDataTemporaryOuting({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.temporaryOutingDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      //   公车申请单数据、打开详情页
      async getOfficialCarDeliveryData(id) {
        this.showOfficialCarDeliveryDetail = true
        await getDataOfficialCarDelivery({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.officialCarDeliveryDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      //   车辆维保单数据、打开详情页
      async getVehicleMaintenanceData(id) {
        await getDataVehicleMaintenance({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        }).then((res) => {
          const data = res.result
          if (data.type === 'project') {
            this.showProjectVehicleMaintenanceDetail = true
            this.$nextTick(() => {
              this.$refs.projectVehicleMaintenanceDetail.showDialog(data)
            })
          } else if (data.type === 'depart') {
            this.showDepartVehicleMaintenanceDetail = true
            this.$nextTick(() => {
              this.$refs.departVehicleMaintenanceDetail.showDialog(data)
            })
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
