<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="合同名称" prop="contractName">
            <el-input
              v-model="queryForm.contractName"
              clearable
              placeholder="请输入合同名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="queryForm.projectName"
              clearable
              placeholder="请输入项目名称"
              style="width: 200px"
            />
          </el-form-item>
           <el-form-item label="回款单位" prop="clienteleName">
            <el-input
              v-model="queryForm.clienteleName"
              clearable
              placeholder="请输入回款单位"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="所属部门" prop="depart">
            <t-form-tree
              v-model="queryForm.depart"
              :default-props="{ children: 'children', label: 'departName' }"
              :tree-data="allDepartList"
            />
          </el-form-item>
          <el-form-item label="审批状态" prop="approvalResult">
            <el-select
              ref="selectFlow"
              v-model="queryForm.approvalResult"
              filterable
              placeholder="请选择审批状态"
              style="width: 200px"
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in approvalResultNameList"
                :key="item.id"
                :label="item.approvalResultName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel>
        <el-button
          v-permissions="{ permission: ['projectPaymentCollection:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
        <!-- <el-button type="danger" icon="el-icon-delete" v-permissions="{ permission: ['projectPaymentCollection:del'] }"
                   @click="delBatch">
          批删
        </el-button> -->
        <el-button
          :disabled="exportLoading"
          icon="el-icon-refresh-right"
          type="warning"
          @click.native="exportBtn('项目回款.xlsx')"
        >
          导出
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      row-key="id"
      :size="lineHeight"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.prop === 'paymentCollectionAmount'">
            {{ row[item.prop] | currencyFormat }}
          </span>
          <span v-else-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['projectPaymentCollection:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 0 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['projectPaymentCollection:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <TableEdit ref="tableEditRef" @fetch-data="fetchData" />

    <TableDetail ref="tableDetail" />
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />
    <VabTableExport ref="tableExport" />
  </div>
</template>

<script>
  import {
    deleteDataPaymentCollectionManagement,
    getDataListByPagePaymentCollectionManagement,
    exportData,
  } from '@/api/financialManagement/paymentCollectionManagement-api'
  import TableEdit from './components/paymentCollectionManagementEdit.vue'
  import TableDetail from './components/paymentCollectionManagementDetail.vue'
  import tableMix from '@/views/mixins/table'
  export default {
    name: 'ProjectPaymentCollection',
    components: {
      TableEdit,
      TableDetail,
    },
    mixins: [tableMix],
    data() {
      return {
        hasCard: true,
        columns: [
          {
            label: '单号',
            prop: 'serialNumber',
            width: '220',
          },
          {
            label: '所属合同',
            prop: 'contractName',
            width: '320',
          },
          {
            label: '所属项目',
            prop: 'projectName',
            width: '320',
          },
          {
            label: '所属部门',
            prop: 'departName',
          },
          {
            label: '本期回款金额(元)',
            prop: 'paymentCollectionAmount',
            width: '150',
          },
          {
            label: '回款单位',
            prop: 'clienteleName',
            width: '320',
          },
          {
            label: '回款说明',
            prop: 'paymentCollectionInstructions',
            width: '150',
          },
          {
            label: '审批状态',
            prop: 'approvalResultName',
            width: '120',
          },
        ],
        listLoading: false,
        //搜索条件必备字段
        queryForm: {
          contractId: '',
          depart: '',
          contractName: '',
          projectName: '',
          type: 'project',
        },
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        //ending
      }
    },
    computed: {
      flowVariables() {
        return {
          projectName: this.flowData.projectName,
          paymentCollectionAmount: this.flowData.paymentCollectionAmount,
          paymentCollectionCompany: this.flowData.paymentCollectionCompany,
          nper: this.flowData.nper,
        }
      },
    },
    created() {
      if (this.$route.query.contractId) {
        this.queryForm.contractId = this.$route.query.contractId
      }
      this.fetchData()
    },
    methods: {
      async fetchData() {
        //async 异步数据处理方法
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.queryForm.selectType = 'project'

        this.listLoading = true
        const { result } = await getDataListByPagePaymentCollectionManagement(
          this.queryForm
        )
        this.list = result.records //把请求到的数据赋值给表格
        this.pageInfo.total = Number(result.total)
        this.listLoading = false
      },
      flowDataVariables() {
        return {
          projectName: this.flowData.projectName,
          paymentCollectionAmount: this.flowData.paymentCollectionAmount,
          paymentCollectionCompany: this.flowData.clienteleName,
          nper: this.flowData.nper,
        }
      },
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportData({ ...this.queryForm, ...columns }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前回款吗', null, async () => {
            deleteDataPaymentCollectionManagement({ id: row.id }).then(
              (response) => {
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              }
            )
          })
        }
      },
      delBatch() {
        if (this.selectRows.length == 0) {
          this.$baseMessage(
            '请最少选中一条记录!',
            'error',
            'vab-hey-message-error'
          )
        } else {
          const ids = this.selectRows.map((item) => item.id).join(',')
          this.$baseConfirm('你确定要批量删除数据吗', null, async () => {
            deleteDataPaymentCollectionManagement({ id: ids })
              .then((response) => {
                this.pageInfo.curPage = 1
                this.fetchData()
                this.$baseMessage(
                  '批量删除成功！',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch((err) => {
                this.$baseMessage(
                  '批量删除失败！',
                  'error',
                  'vab-hey-message-error'
                )
              })
          })
        }
      },
      handleAdd() {
        this.$refs['tableEditRef'].showEdit('project')
      },
      handleEdit(row) {
        this.$refs['tableEditRef'].showEdit('', row)
      },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
    },
  }
</script>

<style lang="scss" scoped></style>
