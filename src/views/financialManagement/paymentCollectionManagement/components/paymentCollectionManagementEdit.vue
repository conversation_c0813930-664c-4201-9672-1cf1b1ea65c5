<template>
  <el-dialog :close-on-click-modal="false" append-to-body :title="title" :visible.sync="dialogFormVisible"
    width="1400px" :before-close="closeBtn" @closed="closeDialog" top="5vh" center v-drag>
    <el-form ref="dataForm" v-loading="formloading" :inline="true" :rules="rules" :model="formData"
      label-position="right" label-width="130px">
      <table v-loading="formloading" class="form-table">
        <tr class="title">
          <td colspan="3">基本信息</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input v-model="formData.serialNumber" style="width: 250px" readonly disabled placeholder="自动生成" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="登记人" prop="createByName">
              <el-input v-model="formData.createByName" style="width: 250px" readonly :disabled="!formData.isAdd" @click.native="selectApplicant" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="回款日期" prop="applicationDate">
              <el-date-picker v-model="formData.applicationDate" format="yyyy-MM-dd" placeholder="选择时间"
                value-format="yyyy-MM-dd" style="width: 250px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <el-form-item label="所属合同" prop="contractId">
              <el-input v-model="formData.contractName" placeholder="选择所属合同" :disabled="!formData.isAdd"
                style="width: 700px" readonly @click.native="selectContract" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="所属部门">
              <el-input :value="departName" placeholder="自动关联" disabled style="width: 250px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <el-form-item label="所属项目" prop="projectId">
              <el-input v-model="formData.projectName" placeholder="自动关联"
                style="width: 700px" disabled />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="回款单位" prop="paymentCollectionCompany">
              <el-input v-model="formData.clienteleName" style="width: 250px" readonly
              @click.native="selectCustomer" placeholder="请选择回款单位">
              </el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="合同金额(元)" prop="contractMoney">
              <t-input-money v-model="formData.contractMoney" placeholder="自动关联" style="width: 250px" disabled
               />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="累计回款(元)">
              <t-input-money :value="totalAmount" placeholder="自动统计" style="width: 250px" disabled
               />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="未回款(元)">
              <t-input-money :value="unPaymentAmount" placeholder="自动统计" style="width: 250px" disabled
                />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="本期回款(元)" prop="paymentCollectionAmount">
              <t-input-money
                v-model="formData.paymentCollectionAmount"
                placeholder="请输入金额(小数位不超过2位)"

                style="width: 250px"


              />
            </el-form-item>
             <!-- @blur="changePaymentCollectionAmount" -->
          </td>
          <td>
            <el-form-item label="累计开票金额(元)" prop="invoicingAmount">
              <t-input-money v-model="formData.invoicingAmount" placeholder="自动统计" style="width: 250px" disabled
               />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="回款期数" prop="nper">
              <el-input type="number" v-model="formData.nper" placeholder="自动生成" disabled style="width:250px;" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="回款说明" prop="paymentCollectionInstructions">
              <el-input v-model="formData.paymentCollectionInstructions" maxlength="1000" placeholder="请输入内容(不超过1000字)"
                style="width: 1180px" type="textarea" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formData.remark" maxlength="1000" placeholder="请输入内容(不超过1000字)" style="width: 1180px"
                type="textarea" />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <!-- 附件上传 -->
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup"></UploadLargeFileFdfsPopup>
    <!-- 选择登记人 -->
    <VabUserListDialog ref="userListPopupRef" @callBackData="getUserInfo" />
    <!-- 合同签署单位选择 -->
    <SelectCustomerDialog ref="selectCustomerRef" @getInfo="getCustomerData" />
    <!-- 合同选择 -->
    <SelectIncomeContract ref="contractListPopupRef" @getContractInfo="getContractInfo" />
    <template #footer>
      <el-button @click="closeBtn">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { mapGetters} from 'vuex'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import {
    saveDataPaymentCollectionManagement,
    getDataListByPagePaymentCollectionManagement,
    getPaymentCollectionMoney,
    getPaymentNper,
  } from '@/api/financialManagement/paymentCollectionManagement-api'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import SelectCustomerDialog from '@/views/contractManagement/common/SelectCustomerDialog.vue'
  import SelectIncomeContract from './SelectIncomeContract.vue'
  export default {
    name: 'TableEdit',
    components: {
      UploadLargeFileFdfsPopup,
      SelectCustomerDialog,
      SelectIncomeContract
    },
    data() {
      const validateReg = (rule, value, callback) => {
               if(value) {
          this.formData.paymentCollectionAmount = value
          // const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
          // const isMoney = reg.test(this.formData.paymentCollectionAmount)
          // if(!isMoney) return
          const allAmount = (this.tempTotalAmount * 100 + this.formData.paymentCollectionAmount * 100) / 100
          if(this.formData.contractMoney * 100 < allAmount * 100) {
            this.$baseMessage(
              '累计回款不能大于合同金额,请重新输入',
              'warning',
              'vab-hey-message-warning'
            )
            callback()
            this.formData.paymentCollectionAmount = 0
            //await this.confirmContract()
          }
          this.totalAmount = allAmount.toFixed(2)
          const unPaymentAmount = (this.formData.contractMoney * 100 - this.totalAmount * 100) / 100
          this.unPaymentAmount = unPaymentAmount.toFixed(2)
           callback()
        } else {
          this.confirmContract()
           callback()
        }
        // const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/ //任意正整数，正小数（小数位不超过2位）
        // if (value && !reg.test(value)) {
        //   callback(new Error('金额格式不对'))
        // } else {
        //   callback()
        // }
      }
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        rules: {
          contractId: {
            required: true,
            message: '请选择所属合同',
            trigger: 'change',
          },
          paymentCollectionAmount: [{
              required: true,
              message: '请输入本期回款',
              trigger: 'blur'
            },
            {
              validator: validateReg,
              trigger: 'change'
            },
          ],
        },
        unPaymentAmount: '',
        totalAmount: '',
        tempTotalAmount: '',
        departName: '',
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
    },
    methods: {
      async showEdit(type, row) {
        this.dialogFormVisible = true
        this.formloading = true
        if (!row) {
          this.title = '新增回款'
          this.dialogStatus = 'create'
          this.initForm(type)
        } else {
          this.title = '回款修改'
          this.dialogStatus = 'update'
          this.formData = Object.assign({
            isAdd: false
          }, row)
          this.departName = this.formData.departName
          this.formloading = true
          await this.confirmContract()
        }
        this.formloading = false
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'paymentCollectionManagement',
          })
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm(type) {
        this.formData = {
          id: genUUID(),
          paymentCollectionInstructions: '',
          projectId: '', //所属项目
          projectName: '',
          contractId: '',
          contractName: '',
          departCode: '',
          paymentCollectionCompany: '',
          clienteleName: '',
          contractMoney: '',
          nper: '',
          invoicingAmount: '',
          paymentCollectionAmount: '',
          selectType: type,
          remark: '', //备注
          createBy: this.userId, //申请人Id
          createByName: this.userName,
          viewUser: this.userId, //申请人名字
          operationCode: type === 'project' ? 'XMHK' : 'JGHK',
          applicationDate: parseTime(new Date(), '{y}-{m}-{d}'), //申请日期
          type,
          isAdd: true,
          serialNumberTable: 'payment_collection_management',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveDataPaymentCollectionManagement(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.dialogFormVisible = false
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      closeBtn() {
        this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        this.$refs['dataForm'].resetFields()
        this.dialogFormVisible = false
      },
      closeDialog() {
        this.formData = this.$options.data().formData
        this.unPaymentAmount = ''
        this.totalAmount = ''
        this.tempTotalAmount = ''
      },
      selectContract() {
        let selectRows = []
        if(this.formData.contractId) {
          selectRows.push({
            id: this.formData.contractId,
            contractName: this.formData.contractName,
          })
        }
        this.$refs.contractListPopupRef.showDialog({
          selectIds: this.formData.contractId,
          selectRows,
        })
      },
      async getContractInfo(data) {
        if(data.length > 0) {
          const {
            id,
            contractName,
            contractRealMoney,
            clienteleName,
            contractSigningUnit,
            projectId,
            projectName,
            depart,
            departName,
            departCode,
          } = data[0]
          this.formData = {
            ...this.formData,
            contractId: id,
            contractName,
            contractMoney: contractRealMoney,
            clienteleName,
            paymentCollectionCompany: contractSigningUnit,
            departCode,
            projectId,
            projectName,
          }
          this.departName = departName
          this.formData.paymentCollectionAmount = ''
          await this.confirmContract()
        }
      },
      selectApplicant() {
        this.$refs.userListPopupRef.showUserDialog({})
      },
      getUserInfo(data) {
        if (data.length > 0) {
          this.formData.applicant = data[0].id
          this.formData.applicantName = data[0].userName
        }
      },
      selectCustomer() {
        this.$refs.selectCustomerRef.showDialog({isNewVersion:'0'})
      },
      getCustomerData(data) {
        if(data.length > 0) {
          this.formData.clienteleName = data[0].accountName
          this.formData.paymentCollectionCompany = data[0].id
        }
      },
      async changePaymentCollectionAmount(row) {
        if(row) {
          this.formData.paymentCollectionAmount = row
          const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
          const isMoney = reg.test(this.formData.paymentCollectionAmount)
          if(!isMoney) return
          const allAmount = (this.tempTotalAmount * 100 + this.formData.paymentCollectionAmount * 100) / 100
          if(this.formData.contractMoney * 100 < allAmount * 100) {
            this.$baseMessage(
              '累计回款不能大于合同金额,请重新输入',
              'warning',
              'vab-hey-message-warning'
            )
            this.formData.paymentCollectionAmount = 0
            await this.confirmContract()
            return
          }
          this.totalAmount = allAmount.toFixed(2)
          const unPaymentAmount = (this.formData.contractMoney * 100 - this.totalAmount * 100) / 100
          this.unPaymentAmount = unPaymentAmount.toFixed(2)
        } else {
          this.confirmContract()
        }
      },
      async confirmContract() {
        if(!this.formData.contractId) return
        if(this.formData.isAdd) {
          const { result:nper } = await getPaymentNper({contractId: this.formData.contractId})
          const params = {
            nper,
            contractId: this.formData.contractId
          }
          const { result: {totalAmount, invoicingAmount}} = await getPaymentCollectionMoney(params)
          this.totalAmount = totalAmount
          this.tempTotalAmount = totalAmount
          this.formData.nper = nper
          this.formData.invoicingAmount = invoicingAmount
        } else {
          const params = {
            nper: this.formData.nper,
            contractId: this.formData.contractId
          }
          const { result: {totalAmount}} = await getPaymentCollectionMoney(params)
          const allAmount = (totalAmount * 100 + this.formData.paymentCollectionAmount * 100) / 100
          this.totalAmount = allAmount.toFixed(2)
          this.tempTotalAmount = totalAmount
        }
        const unPaymentAmount = (this.formData.contractMoney * 100 - this.totalAmount * 100) / 100
        this.unPaymentAmount = unPaymentAmount.toFixed(2)
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-input__inner {
    padding-right: 0;
    &::-webkit-inner-spin-button {
      margin: 0;
      -webkit-appearance: none !important;
    }
    &::-webkit-outer-spin-button {
      margin: 0;
      -webkit-appearance: none !important;
    }
  }
</style>
