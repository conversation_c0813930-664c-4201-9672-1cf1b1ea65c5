<template>
  <div class="todo-box">
    <div class="approval-list" @click="viewMore">
      待办事项
      <span>{{ myToDoTotal }}</span>
      <div class="r_img"><img alt="" src="~@/assets/work_icon1.png" /></div>
    </div>
    <div class="approval-content">
      <div class="box-head">
        <span>待办事项</span>
        <a href="javascript:void(0)" @click="viewMore">更多</a>
      </div>
      <div class="box-content">
        <div v-if="myToDoData.length > 0" class="list">
          <div
            v-for="item in myToDoData"
            :key="'todo' + item.id"
            class="item"
            @click="pageJump(item)"
          >
            <div class="itme_flex">
              <!--              <div class="isRead">-->
              <!--                <el-tag v-if="item.isRead === '0'" size="mini" type="danger">-->
              <!--                  未读-->
              <!--                </el-tag>-->
              <!--                <el-tag v-else size="mini" type="success">已读</el-tag>-->
              <!--              </div>-->
              <div class="con">
                <a :title="item.titleName">{{ item.titleName }}</a>
                <div class="timeBox">
                  <div class="time">
                    {{ item.createTime | dateformat('YYYY-MM-DD') }}
                  </div>
                  <div class="date">
                    {{ item.createTime | dateformat('HH:mm:ss') }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <NoData v-else>暂无事项</NoData>
      </div>
    </div>
  </div>
</template>

<script>
  import NoData from './noData.vue'
  import { getToDoByPage } from '@/api/workflow/workflow-api.js'
  import { mapGetters } from 'vuex'

  export default {
    components: {
      NoData,
    },
    data() {
      return {
        myToDoTotal: 0,
        myToDoData: [],
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        oneDimensionRoutes: 'routes/oneDimensionRoutes',
      }),
    },
    created() {
      this.getToDoByPage()
    },
    methods: {
      getToDoByPage() {
        const queryData = {}
        queryData.curPage = 1
        queryData.pageSize = 500
        queryData.userId = this.userId
        getToDoByPage(queryData).then((response) => {
          this.myToDoData = response.result.records
          this.myToDoTotal = Number(response.result.total)
        })
      },
      pageJump(item) {
        const routePath = this.oneDimensionRoutes.find(
          (val) => val.name === item.bizCode
        )
        if (routePath) {
          const path = `${routePath.path}?id=${item.id}&&reach=approval`
          this.$openPage(path, routePath.path)
        } else {
          this.$baseMessage(
            '跳转失败，找不到页面路径。',
            'error',
            'vab-hey-message-error'
          )
        }
      },
      viewMore() {
        const path = '/personalCenter/toDo'
        this.$openPage(path)
      },
    },
  }
</script>
