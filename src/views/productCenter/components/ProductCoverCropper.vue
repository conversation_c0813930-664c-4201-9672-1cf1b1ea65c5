<template>
  <div>
    <!-- 产品封面上传组件使用示例 -->
    <cover-cropper-product
      :formDataId="productId"
      :imageUrl="productCoverUrl"
      :dialogStatus="dialogStatus"
      :aspectWidth="398"
      :aspectHeight="215"
      :maxPreviewWidth="350"
      fileNamePrefix="productCover"
      @getImgUrl="handleCoverUpload"
    />
  </div>
</template>

<script>
import CoverCropperProduct from '@/views/enterpriseManagement/newsManagement/components/coverCropperProduct.vue'

export default {
  name: 'ProductCoverCropper',
  components: {
    CoverCropperProduct
  },
  props: {
    productId: {
      type: String,
      required: true
    },
    coverUrl: {
      type: String,
      default: ''
    },
    mode: {
      type: String,
      default: 'add' // add | edit
    }
  },
  data() {
    return {
      productCoverUrl: ''
    }
  },
  computed: {
    dialogStatus() {
      return this.mode === 'edit' ? 'update' : 'add'
    }
  },
  watch: {
    coverUrl: {
      immediate: true,
      handler(newVal) {
        this.productCoverUrl = newVal
      }
    }
  },
  methods: {
    handleCoverUpload(imageUrl) {
      this.productCoverUrl = imageUrl
      this.$emit('coverUploaded', imageUrl)
    }
  }
}
</script>

<style scoped>
/* 产品封面特定样式 */
</style>
