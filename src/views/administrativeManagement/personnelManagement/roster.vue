<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <div v-loading="totalLoading" class="statistics-total">
          <div class="item">
            <div class="img-icon">
              <img alt="" src="@/assets/roster_icon1.png" />
            </div>
            <div class="num-info">
              <div class="value">
                {{ workingTotal }}
                <span>人</span>
              </div>
              <div class="name">正式员工</div>
            </div>
          </div>
          <div class="item">
            <div class="img-icon">
              <img alt="" src="@/assets/roster_icon2.png" />
            </div>
            <div class="num-info">
              <div class="value">
                {{ tryOutTotal }}
                <span>人</span>
              </div>
              <div class="name">试用员工</div>
            </div>
          </div>
          <div class="item">
            <div class="img-icon">
              <img alt="" src="@/assets/roster_icon3.png" />
            </div>
            <div class="num-info">
              <div class="value">
                {{ practiceTotal }}
                <span>人</span>
              </div>
              <div class="name">实习员工</div>
            </div>
          </div>
          <div class="item">
            <div class="img-icon">
              <img alt="" src="@/assets/roster_icon4.png" />
            </div>
            <div class="num-info">
              <div class="value">
                {{ partTimeTotal }}
                <span>人</span>
              </div>
              <div class="name">兼职员工</div>
            </div>
          </div>
          <div class="item">
            <div class="img-icon">
              <img alt="" src="@/assets/roster_icon6.png" />
            </div>
            <div class="num-info">
              <div class="value">
                {{ demissionTotal }}
                <span>人</span>
              </div>
              <div class="name">离职员工</div>
            </div>
          </div>
        </div>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="所属部门" prop="departId">
            <t-form-tree
              v-model="queryForm.departId"
              :default-props="{ children: 'children', label: 'departName' }"
              :show-top="false"
              :tree-data="departTreeData"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="userName">
            <el-input
              v-model="queryForm.userName"
              placeholder="请输入姓名"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="员工状态" prop="employeeStatus">
            <el-select
              v-model="queryForm.employeeStatus"
              clearable
              placeholder="请选择员工状态"
              style="width: 200px"
            >
              <el-option
                v-for="item in employeeStatusList"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="主要专业" prop="profession">
            <el-input
              v-model="queryForm.profession"
              placeholder="请输入专业"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item
            v-show="!fold"
            label="职称"
            prop="titleProfessionalPost"
          >
            <el-select
              v-model="queryForm.titleProfessionalPost"
              clearable
              placeholder="请选择职称"
              style="width: 200px"
            >
              <el-option
                v-for="item in titleProfessionalPostList"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="!fold" label="学历" prop="eduBackground">
            <el-select
              v-model="queryForm.eduBackground"
              clearable
              placeholder="请选择学历"
              style="width: 200px"
            >
              <el-option
                v-for="item in eduBackgroundList"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="!fold" label="入职时间" prop="onBoardTime">
            <el-date-picker
              v-model="queryForm.onBoardTime"
              end-placeholder="结束日期"
              range-separator="至"
              start-placeholder="开始日期"
              type="daterange"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
          <el-button
            style="margin: 0 0 0 10px !important"
            type="text"
            @click="handleFold"
          >
            <span v-if="fold">展开</span>
            <span v-else>合并</span>
            <vab-icon
              class="vab-dropdown"
              :class="{ 'vab-dropdown-active': fold }"
              icon="arrow-up-s-line"
            />
          </el-button>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <!--        <el-button
          v-permissions="{ permission: ['roster:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
        <el-button
          v-permissions="{ permission: ['roster:del'] }"
          icon="el-icon-delete"
          type="danger"
          @click="batchDelete"
        >
          批删
        </el-button>-->

        <el-button
          :disabled="exportLoading"
          icon="el-icon-download"
          type="warning"
          @click.native="exportBtn('花名册记录.xlsx')"
        >
          导出
        </el-button>
        <!-- <el-button icon="el-icon-refresh-right" type="warning"
        :disabled="exportLoading"
        @click.native="exportBtn"
        >
          导出
        </el-button> -->
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.prop === 'onBoardTime'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <!--          年龄-->
          <span v-else-if="item.label === '年龄'">
            {{ getAge(row.idCard) }}
          </span>
          <span v-else-if="item.label === '排序'" class="inputCenter">
            <el-input
              v-model="row.sort"
              placeholder=""
              @change="handleSortChange(row)"
            />
          </span>
          <!--员工状态-->
          <!--          <span v-else-if="item.label === '员工状态'">-->
          <!--            {{getTypeNameByIdA(row[item.prop])}}-->
          <!--          </span>-->
          <!--          <span v-else-if="item.label === '职级'">-->
          <!--            {{getTypeNameByIdB(row[item.prop])}}-->
          <!--          </span>-->
          <!--          <span v-else-if="item.label === '职称'">-->
          <!--            {{getTypeNameByIdC(row[item.prop])}}-->
          <!--          </span>-->
          <!-- <span v-else-if="item.label === '部门'">
            {{ getTypeNameByDepartList(row[item.prop]) }}
          </span> -->
          <!--          <span v-else-if="item.label === '学历'">-->
          <!--            {{getTypeNameByIdD(row[item.prop])}}-->
          <!--          </span>-->
          <span v-else-if="item.label === '司龄'">
            {{ getSurname(row.onBoardTime) }}
          </span>
          <span
            v-else-if="item.prop === 'employeeStatusName'"
            :class="{
              'eyStatus status-c1': row[item.prop] === '正式',
              'eyStatus status-c2': row[item.prop] === '离职',
              'eyStatus status-c3': row[item.prop] === '试用',
              'eyStatus status-c4': row[item.prop] === '实习',
              'eyStatus status-c6': row[item.prop] === '见习',
              'eyStatus status-c5': row[item.prop] === '兼职',
            }"
          >
            {{ row[item.prop] }}
          </span>

          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="130">
        <template #default="{ row }">
          <el-button
            v-if="userAccount === 'jxth'"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <!--          <el-button
            v-permissions="{ permission: ['roster:del'] }"
            icon="el-icon-delete"
            style="margin: 0 10px 10px 0 !important"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>-->
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-detail ref="tableDetail" />
    <table-edit ref="tableEdit" @fetch-data="fetchData" />
    <VabTableExport ref="tableExport" @refreshPage="resetFormPage" />
  </div>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import tableDetail from './components/rosterDetail.vue'
  import tableEdit from './components/rosterEdit.vue'
  import { getDictList } from '@/api/system/dict-api' //数据字典api
  import { getDepartList } from '@/api/system/depart-api'
  import {
    saveData,
    getDataListByPage,
    deleteData,
    getEmployeeTotal,
    getMaxSort,
    exportDatas,
  } from '@/api/staffManagement/rosterApi'
  import { mapGetters } from 'vuex'
  export default {
    name: 'Roster',
    components: {
      tableDetail,
      tableEdit,
    },
    mixins: [tableMix],
    data() {
      return {
        formMinHeight: 3,
        formMaxHeight: 4,
        columns: [
          {
            label: '员工状态',
            prop: 'employeeStatusName',
            width: '100',
          },
          {
            label: '姓名',
            prop: 'userName',
            width: '120',
          },
          {
            label: '身份证号',
            prop: 'idCard',
            width: '180',
          },
          {
            label: '年龄',
            prop: 'age',
            width: '80',
          },
          {
            label: '性别',
            prop: 'genderName',
            width: '80',
          },
          {
            label: '部门',
            prop: 'departName',
            width: '140',
          },
          {
            label: '职级',
            prop: 'jobRankName',
            width: '140',
          },
          {
            label: '职称',
            prop: 'titleProfessionalPostName',
          },
          {
            label: '主要专业',
            prop: 'profession',
            width: '160',
          },
          {
            label: '学历',
            prop: 'eduBackgroundName',
          },
          {
            label: '入职时间',
            prop: 'onBoardTime',
            width: '120',
          },
          {
            label: '司龄',
            prop: 'companyAge',
            width: '160',
          },
          {
            label: '手机号',
            prop: 'telephone',
            width: '120',
          },
          {
            label: '排序',
            prop: 'sort',
            width: '90',
          },
        ],
        queryForm: {
          onBoardTime: '', //入职时间
          startTime: '',
          endTime: '',
          departId: '', //所属部门
          userName: '', //姓名
          employeeStatus: '', //员工状态
          titleProfessionalPost: '', //职称
          eduBackground: '', //学历
          profession: '', //主要专业
        },
        employeeStatusList: [],
        titleProfessionalPostList: [],
        eduBackgroundList: [],
        professionList: [],
        departTreeData: [],
        rankList: [],
        // 试用人员统计
        tryOutTotal: null,
        // 在职人员统计
        workingTotal: null,
        // 实习人员统计
        practiceTotal: null,
        // 兼职人员统计
        partTimeTotal: null,
        demissionTotal: null,
        sort: 1,
        totalLoading: false,
      }
    },
    computed: {
      ...mapGetters({
        userAccount: 'user/userAccount',
      }),
    },
    created() {
      this.getDictDetailsByCode() //  获取数据字典
      this.getDepartList() // 获取部门数据
      this.fetchData() // 获取页面数据
    },
    methods: {
      getDepartList() {
        getDepartList({}).then((response) => {
          //获取所属部门数据
          this.departTreeData = response.result
        })
      },
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportDatas({ ...this.queryForm, ...columns }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },

      // 获取多个数据字典
      getDictDetailsByCode() {
        getDictList({ dictCode: 'employeeStatus,jobTitle,degreeType' }).then(
          (response) => {
            response.result.forEach((item) => {
              if (item.dictCode === 'employeeStatus') {
                this.employeeStatusList = item.children
              }
              if (item.dictCode === 'jobTitle') {
                this.titleProfessionalPostList = item.children
              }
              if (item.dictCode === 'degreeType') {
                this.eduBackgroundList = item.children
              }
            })
          }
        )
      },
      getTypeNameByDepartList(departList) {
        if (!departList) return ''
        // console.log(departList)
        const nameData = []
        departList.forEach((item) => {
          nameData.push(item.departName)
        })
        return nameData.join('、')
      },
      // 获取最大序号
      getMaxSort() {
        getMaxSort().then((res) => {
          this.sort = res.result
        })
      },
      // 计算年龄
      getAge(idCard) {
        if (idCard === '') return
        // 计算年龄
        // 提取出生日期
        const birthYear = parseInt(idCard.substring(6, 10), 10)
        const birthMonth = parseInt(idCard.substring(10, 12), 10) - 1 // 月份从0开始
        const birthDay = parseInt(idCard.substring(12, 14), 10)
        const birthDate = new Date(birthYear, birthMonth, birthDay)
        // 获取当前日期
        const today = new Date()
        return today.getFullYear() - birthDate.getFullYear()
      },
      getSurname(onBoardTime) {
        if (onBoardTime) {
          const onBoardDate = new Date(onBoardTime)
          const currentDate = new Date()
          let years = currentDate.getFullYear() - onBoardDate.getFullYear()
          let months = currentDate.getMonth() - onBoardDate.getMonth()
          let days = currentDate.getDate() - onBoardDate.getDate()

          if (days < 0) {
            months -= 1
            days += new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              0
            ).getDate()
          }
          if (months < 0) {
            years -= 1
            months += 12
          }

          let result = ''
          if (years > 0) {
            result += `${years}年`
          }
          if (months > 0) {
            result += `${months}个月`
          }
          if (days > 0) {
            result += `${days}天`
          }

          // 如果所有值都为0，则返回0天
          if (result === '') {
            result = '0天'
          }

          return result
        } else {
          return ''
        }
      },
      async fetchData() {
        this.listLoading = true
        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        if (this.queryForm.onBoardTime) {
          queryForm.startTime = this.queryForm.onBoardTime[0]
          queryForm.endTime = this.queryForm.onBoardTime[1]
          // console.log(this.queryForm);
        } else {
          queryForm.startTime = ''
          queryForm.endTime = ''
        }
        await getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
          //  获取员工状态统计
          this.getEmployeeTotal()
          this.getMaxSort() //作用于新增员工时获取最大序号
        })
      },
      async fetchDataPx() {
        this.listLoading = true
        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        if (this.queryForm.onBoardTime) {
          queryForm.startTime = this.queryForm.onBoardTime[0]
          queryForm.endTime = this.queryForm.onBoardTime[1]
          // console.log(this.queryForm);
        } else {
          queryForm.startTime = ''
          queryForm.endTime = ''
        }
        await getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },

      //  获取员工状态统计
      async getEmployeeTotal() {
        this.totalLoading = true
        const predefinedCodes = [
          'tryOut',
          'working',
          'practice',
          'partTime',
          'demission',
        ] // 所有可能的dictCode
        const totals = {} // 临时对象存储加载的数据
        await getEmployeeTotal({})
          .then((res) => {
            const result = res.result
            // 初始化所有预定义的字段为 null
            predefinedCodes.forEach((code) => {
              totals[code + 'Total'] = 0
            })
            // 更新有数据的字段
            result.forEach((item) => {
              let code = item.dictCode + 'Total'
              totals[code] = item.total
            })
            // 数据加载完毕后一次性更新到组件状态中
            predefinedCodes.forEach((code) => {
              this[code + 'Total'] = totals[code + 'Total']
            })
            this.totalLoading = false
          })
          .catch((err) => {
            console.log(err)
            this.totalLoading = false
          })
      },

      handleEdit(row) {
        this.$refs['tableEdit'].showEdit(row)
      },
      handleAdd() {
        this.$refs['tableEdit'].showEdit(null, this.sort)
      },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm(
            '你确定要删除该人员及其合同、学历、证书、工资卡信息吗？',
            null,
            async () => {
              deleteData({ id: row.id })
                .then(() => {
                  this.fetchData()
                  this.$baseMessage(
                    '删除成功!',
                    'success',
                    'vab-hey-message-success'
                  )
                })
                .catch(() => {
                  this.$baseMessage(
                    '删除失败!',
                    'error',
                    'vab-hey-message-error'
                  )
                })
            }
          )
        }
      },
      async handleSortChange(row) {
        // 检查排序值是否为空
        if (row.sort === '') {
          this.$message.warning('请输入排序号')
          this.fetchDataPx()
          return
        }
        // 检查新值是否与旧值不同
        if (row.sort) {
          let data = { ...row, isAdd: false }
          try {
            await saveData(data)
            this.$message.success('排序值已更新')
            this.fetchDataPx()
          } catch (error) {
            console.error('更新排序值失败', error)
            this.$message.error('更新排序值失败')
          }
        }
      },
      // 批删除
      batchDelete() {
        if (this.selectRows.length === 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
          return
        }
        const idArr = this.selectRows.map((item) => item.id).join(',')
        this.$baseConfirm('你确定要删除所有选中吗', null, async () => {
          deleteData({ id: idArr })
            .then(() => {
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
        })
      },
    },
  }
</script>
<style>
  .inputCenter .el-input__inner {
    text-align: center;
  }
</style>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
  .statistics-total {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin: 10px 0 25px 0;
    font-size: 18px;
    .item {
      display: flex;
      justify-content: center;
      width: 20%;
      text-align: center;
      &:nth-child(2) {
        .num-info {
          .value {
            color: #f98031;
          }
        }
      }
      &:nth-child(3) {
        .num-info {
          .value {
            color: #a131f9;
          }
        }
      }
      &:nth-child(4) {
        .num-info {
          .value {
            color: #29cafd;
          }
        }
      }
      &:nth-child(5) {
        .num-info {
          .value {
            color: #ff6767;
          }
        }
      }
      .img-icon {
        margin-right: 20px;
        img {
          width: 60px;
        }
      }
      .num-info {
        .value {
          color: #588df9;
          font-weight: bold;
          font-size: 28px;
          font-family: Arial, Helvetica Neue, Helvetica, sans-serif;
          height: 32px;
          line-height: 32px;
        }
        span {
          font-size: 14px;
          font-weight: normal;
        }
        .name {
          font-size: 16px;
          color: #8a8a8a;
        }
      }
    }
  }
  .eyStatus {
    padding: 2px 5px;
    border-radius: 5px;
    color: #fff;
  }
  .status-c1 {
    background: #588df9;
  }
  .status-c2 {
    background: #ff6767;
  }
  .status-c3 {
    background: #f98031;
  }
  .status-c4 {
    background: #a131f9;
  }
  .status-c5 {
    background: #29cafd;
  }
  .status-c6 {
    background: #f98031;
  }
</style>
