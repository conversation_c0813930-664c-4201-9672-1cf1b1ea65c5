<template>
  <el-dialog
    v-drag
    append-to-body
    center
    class="detailDialog"
    :close-on-click-modal="false"
    title="花名册详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1300px"
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-tabs
        v-model="activeName"
        v-loading="formLoading"
        type="card"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="基本信息" name="first">
          <el-descriptions
            border
            class="margin-top"
            :column="3"
            :label-style="{ width: '100px' }"
            size="medium"
          >
            <el-descriptions-item>
              <template slot="label">姓名</template>
              {{ formData.userName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">年龄</template>
              {{ $parent.getAge(formData.idCard) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">手机号</template>
              {{ formData.telephone }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">所属部门</template>
              {{ $parent.getTypeNameByDepartList(formData.departList) }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">性别</template>
              {{ formData.genderName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">职级</template>
              {{ formData.jobRankName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">职称</template>
              {{ formData.titleProfessionalPostName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">最高学历</template>
              {{ formData.eduBackgroundName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">身份证号</template>
              {{ formData.idCard }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">邮箱</template>
              {{ formData.email }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">员工状态</template>
              {{ formData.employeeStatusName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">薪资类型</template>
              {{ formData.employeeTypeName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">薪资等级</template>
              {{ formData.payScaleName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">薪资(元）</template>
              {{ formData.payMoney }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">入职时间</template>
              {{ formData.onBoardTime | dateformat('YYYY-MM-DD') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">司龄</template>
              {{ $parent.getSurname(formData.onBoardTime) }}
            </el-descriptions-item>
          </el-descriptions>
          <!--     附件      -->
          <div class="ts">
            <strong>身份证正反面</strong>
            附件
          </div>
          <UploadLargeFileFdfsPopupDetail
            ref="UploadLargeFileFdfsPopupDetail"
            style="margin-top: 20px"
          />
        </el-tab-pane>
        <el-tab-pane label="合同信息" name="second">
          <el-descriptions
            border
            class="margin-top"
            :column="3"
            :label-style="{ width: '120px' }"
            size="medium"
          >
            <el-descriptions-item>
              <template slot="label">合同公司</template>
              {{ contractData.contractCompany }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">合同类型</template>
              {{ contractData.contractTypeName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">合同期限</template>
              {{ contractData.termContractName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">首次合同起始日</template>
              {{ contractData.firstStartTime | dateformat('YYYY-MM-DD') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">现合同起始日</template>
              {{ contractData.nowStartTime | dateformat('YYYY-MM-DD') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">签约次数</template>
              {{ contractData.numberContracts }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">首次合同到期日</template>
              {{ contractData.firstEndTime | dateformat('YYYY-MM-DD') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">现合同到期日</template>
              {{ contractData.nowEndTime | dateformat('YYYY-MM-DD') }}
            </el-descriptions-item>
          </el-descriptions>
          <div class="ts">
            <strong>员工合同</strong>
            附件
          </div>
          <UploadLargeFileFdfsPopupDetail ref="ContractAttachment" />
        </el-tab-pane>
        <el-tab-pane label="学历信息" name="third">
          <el-descriptions
            border
            class="margin-top"
            :column="3"
            :label-style="{ width: '100px' }"
            size="medium"
          >
            <el-descriptions-item>
              <template slot="label">毕业院校</template>
              {{ educationData.graduateSchool }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">学历</template>
              {{ educationData.eduBackgroundName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">毕业时间</template>
              {{ educationData.graduationTime | dateformat('YYYY-MM-DD') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">专业</template>
              {{ educationData.profession }}
            </el-descriptions-item>
          </el-descriptions>
          <div class="ts">
            <strong>学历</strong>
            附件
          </div>
          <UploadLargeFileFdfsPopupDetail ref="EducationalAttachment" />
        </el-tab-pane>
        <el-tab-pane label="证书信息" name="four">
          <el-table
            ref="tableSort"
            v-loading="certificateListLoading"
            border
            :data="certificateDataList"
            label-width="90px"
            :max-height="450"
            stripe
          >
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="80"
            />
            <el-table-column
              align="center"
              label="证件类别"
              prop="certificateType"
              width="320"
            />
            <el-table-column
              align="center"
              label="证件编号"
              prop="certificateNumber"
              width="220"
            />
            <el-table-column
              align="center"
              label="主要专业"
              prop="profession"
            />
            <el-table-column
              align="center"
              label="证件有效期"
              prop="certificateValidity"
            >
              <template #default="{ row }">
                <span v-if="row.startTime">
                  {{ dateFormat(row.startTime) }}
                  &nbsp;至&nbsp;
                  {{
                    dateFormat(row.endTime) ? dateFormat(row.endTime) : '永久'
                  }}
                </span>
              </template>
            </el-table-column>
          </el-table>
          <div class="ts">
            <strong>证书</strong>
            附件
          </div>
          <UploadLargeFileFdfsPopupDetail ref="CertificateAttachment" />
        </el-tab-pane>
        <el-tab-pane label="工资卡信息" name="five">
          <el-table
            ref="tableSort2"
            v-loading="salaryCardListLoading"
            border
            :data="salaryCardDataList"
            label-width="90px"
            :max-height="450"
            stripe
          >
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="80"
            />
            <el-table-column
              align="center"
              label="开户行名称"
              prop="bankDeposit"
            />
            <el-table-column
              align="center"
              label="工资卡号"
              prop="cardNumber"
            />
          </el-table>
          <div class="ts">
            <strong>工资卡</strong>
            附件
          </div>
          <UploadLargeFileFdfsPopupDetail ref="SalaryCardAttachment" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  import { getDictList } from '@/api/system/dict-api'
  import {
    getDataListCard,
    getDataListCertificate,
    getDataListContract,
    getDataListEdu,
  } from '@/api/staffManagement/rosterApi'
  import CommonUploadListPopup from '@/views/common/CommonUploadListPopup.vue'
  import { parseTime } from '@/utils'

  export default {
    name: 'RosterDetail',
    components: { CommonUploadListPopup, UploadLargeFileFdfsPopupDetail },
    props: {},

    data() {
      return {
        // 自定义标签样式
        labelStyle: {
          width: '120px',
        },
        dialogDetailVisible: false,
        formLoading: false,
        certificateListLoading: false,
        salaryCardListLoading: false,
        // 基本信息
        formData: {
          sort: 1,
          idCard: '',
        },
        // 合同信息
        contractData: {},
        // 学历信息
        educationData: {},
        // 证书列表
        certificateDataList: [],
        // 工资卡列表
        salaryCardDataList: [],
        // 薪资类型列表
        employeeTypeList: [],
        activeName: 'first',
      }
    },
    created() {},
    mounted() {},
    methods: {
      dateFormat(date) {
        if (!date) {
          return ''
        }
        return parseTime(new Date(date), '{y}-{m}-{d}')
      },
      // 详情弹窗
      async showDialog(row) {
        this.activeName = 'first'
        this.formData = { ...row }
        this.dialogDetailVisible = true
        this.formLoading = true
        // 获取合同信息
        await this.getDataListContract(row.id)
        // 获取学历信息
        await this.getDataEdu(row.id)
        // 获取证书信息列表
        await this.getCertificateDataList(row.id)
        // 获取工资卡信息列表
        await this.getSalaryCardDataList(row.id)

        this.$nextTick(() => {
          // 身份证附件
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: 'rosterBaseInfo',
            isShow: true,
          })
          // 合同附件 bizId正常是合同数据的id
          this.$refs.ContractAttachment.getParamsData({
            bizId: this.contractData.id || this.formData.id,
            bizCode: 'contractAttachment',
            isShow: true,
          })
          // 学历附件 bizId正常是合同数据的id
          this.$refs.EducationalAttachment.getParamsData({
            bizId: this.educationData.id || this.formData.id,
            bizCode: 'EducationalAttachment',
            isShow: true,
          })
          // 证书附件
          this.$refs.CertificateAttachment.getParamsData({
            bizId: this.formData.id,
            bizCode: 'CertificateAttachment',
            isShow: true,
          })
          // 工资卡附件
          this.$refs.SalaryCardAttachment.getParamsData({
            bizId: this.formData.id,
            bizCode: 'SalaryCardAttachment',
            isShow: true,
          })
        })
        this.formLoading = false
      },
      // 证书附件
      showUploadFileList(row, bizCode) {
        const data = {}
        data.bizId = row.id
        data.bizCode = bizCode
        data.title = '证书附件列表'
        data.isShow = true
        this.$refs.CommonUploadListPopup.showUploadListDialog(data)
      },
      // 获取合同信息
      async getDataListContract(id) {
        await getDataListContract({ employeeId: id }).then((response) => {
          this.contractData =
            response.result.length > 0 ? response.result[0] : []
        })
      },
      // 获取学历信息
      async getDataEdu(id) {
        await getDataListEdu({ employeeId: id }).then((response) => {
          this.educationData =
            response.result.length > 0 ? response.result[0] : []
        })
      },
      // 获取证书信息列表
      async getCertificateDataList(id) {
        await getDataListCertificate({ employeeId: id }).then((response) => {
          this.certificateDataList = response.result.map((item) => ({
            ...item,
            profession: item.profession || this.educationData.profession, // 为每个表格项添加 profession 属性
          }))
        })
      },
      // 获取工资卡信息列表
      async getSalaryCardDataList(id) {
        await getDataListCard({ employeeId: id }).then((response) => {
          this.salaryCardDataList = response.result
        })
      },
      // 切换tab
      handleTabClick(tab) {
        switch (tab.name) {
          case 'four':
            // 模拟加载效果
            this.certificateListLoading = true
            setTimeout(() => {
              this.certificateListLoading = false
            }, 300)
            break
          case 'five':
            this.salaryCardListLoading = true
            setTimeout(() => {
              this.salaryCardListLoading = false
            }, 300)
            break
        }
      },
    },
  }
</script>
<style lang="scss" scoped>
  .detailDialog ::v-deep .el-dialog__body {
    padding-top: 10px;
  }
  .statistics {
    width: 100%;
    font-size: 18px;
    margin: 10px 0;
    display: flex;
    justify-content: space-around;
    .item {
      display: flex;
      justify-content: space-between;
      .number {
        color: #e53f3f;
      }
    }
  }
</style>
