<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="学历详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1040px"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
    >
      <el-descriptions
        border
        class="margin-top"
        :column="3"
        :label-style="labelStyle"
        size="medium"
      >
        <el-descriptions-item>
          <template slot="label">姓名</template>
          {{ formData.userName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">部门</template>
          {{ $parent.getTypeNameByDepartList(formData.departList) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">职级</template>
          {{ formData.jobRankName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">毕业院校</template>
          {{ formData.graduateSchool }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">学历</template>
          {{ formData.eduBackgroundName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">毕业时间</template>
          {{ formData.graduationTime | formateTime }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">专业</template>
          {{ formData.profession }}
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <!-- 附件上传 -->
    <UploadLargeFileFdfsPopupDetail
      ref="UploadLargeFileFdfsPopupDetail"
      style="margin-top: 20px"
    />
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    filters: {
      formateTime(val) {
        if (!val) return ''
        return val.substr(0, 11)
      },
    },
    data() {
      return {
        dialogDetailVisible: false,
        formLoading: false,
        formData: {},
        labelStyle: {
          width: '130px',
        },
      }
    },
    computed: {},
    created() {
      // this.getDictDetailsByCode()
    },
    methods: {
      async showDialog(row) {
        this.formLoading = true
        this.formData = { ...row }
        this.formLoading = false
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: 'EducationalAttachment',
            isShow: true,
          })
          this.$refs['dataForm'].clearValidate()
        })
        this.dialogDetailVisible = true
      },
      // callBackRefresh(){
      //   this.$parent.fetchData()
      //   this.dialogDetailVisible = false
      // },
    },
  }
</script>

<style lang="scss" scoped></style>
