<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="actionMap[dialogStatus]"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1420px"
    @close="close"
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-tabs
        v-model="activeName"
        v-loading="formLoading"
        :before-leave="beforeLeave"
        type="card"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="基本信息" name="first">
          <el-form
            ref="dataForm"
            v-loading="formLoading"
            :inline="true"
            label-position="right"
            label-width="140px"
            :model="formData"
            :rules="rules"
          >
            <table class="form-table">
              <tr>
                <td>
                  <el-form-item label="排序" prop="sort">
                    <el-input
                      v-model.number="formData.sort"
                      placeholder="自动生成"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="姓名" prop="userId">
                    <t-form-user
                      v-model="formData.userId"
                      :disabled="!formData.isAdd"
                      :multiple="false"
                      placeholder="请选择"
                      @update-user-info="handleUserInfoUpdate"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="年龄" prop="age">
                    <el-input v-model="age" disabled placeholder="自动计算" />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="手机号" prop="telephone">
                    <el-input
                      v-model="formData.telephone"
                      disabled
                      placeholder="自动关联"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="所属部门" prop="departName">
                    <el-input
                      v-model="formData.departName"
                      disabled
                      placeholder="自动关联"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="性别" prop="genderName">
                    <el-input
                      v-model="formData.genderName"
                      disabled
                      placeholder="自动关联"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="职级" prop="jobRank">
                    <el-select
                      v-model="formData.jobRank"
                      clearable
                      placeholder="请选择职级"
                    >
                      <el-option
                        v-for="item in rankList"
                        :key="item.id"
                        :label="item.dictName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="职称" prop="titleProfessionalPost">
                    <el-select
                      v-model="formData.titleProfessionalPost"
                      clearable
                      placeholder="请选择职称"
                    >
                      <el-option
                        v-for="item in titleProfessionalPostList"
                        :key="item.id"
                        :label="item.dictName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="最高学历" prop="eduBackground">
                    <!--                    <el-input v-model="formData.eduBackground" placeholder="自动关联" readonly  disabled />-->
                    <el-select
                      v-model="formData.eduBackground"
                      clearable
                      disabled
                      placeholder="自动关联"
                    >
                      <el-option
                        v-for="item in degreeTypeList"
                        :key="item.id"
                        :label="item.dictName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="身份证号" prop="idCard">
                    <el-input
                      v-model="formData.idCard"
                      placeholder="请输入身份证号"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="邮箱" prop="email">
                    <el-input
                      v-model="formData.email"
                      disabled
                      placeholder="自动关联"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="员工状态" prop="employeeStatus">
                    <el-select
                      v-model="formData.employeeStatus"
                      clearable
                      placeholder="请选择员工状态"
                    >
                      <el-option
                        v-for="item in employeeStatusList"
                        :key="item.id"
                        :disabled="item.disabled"
                        :label="item.dictName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="薪资类型" prop="employeeType">
                    <el-select
                      v-model="formData.employeeType"
                      clearable
                      placeholder="请选择薪资类型"
                      @change="changeEmployeeType"
                    >
                      <el-option
                        v-for="item in employeeTypeList"
                        :key="item.id"
                        :label="item.dictName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item
                    label="薪资等级"
                    prop="payScale"
                    :rules="
                      !payScaleDisabled
                        ? {
                            required: true,
                            message: '请选择薪资等级',
                            trigger: 'change',
                          }
                        : {}
                    "
                  >
                    <el-select
                      v-model="formData.payScale"
                      clearable
                      :disabled="payScaleDisabled"
                      filterable
                      placeholder="请选择薪资等级"
                      @change="changePayScale"
                    >
                      <el-option
                        v-for="item in payScaleList"
                        :key="item.id"
                        :label="item.dictName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="薪资(元）" prop="payMoney">
                    <el-input-number
                      v-model="formData.payMoney"
                      :controls="false"
                      :disabled="payMoneyDisabled"
                      :min="0"
                      placeholder="请输入薪资金额"
                      :precision="0"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="入职时间" prop="onBoardTime">
                    <el-date-picker
                      v-model="formData.onBoardTime"
                      placeholder="选择入职时间"
                      type="date"
                    />
                  </el-form-item>
                </td>
                <td :colspan="2">
                  <el-form-item label="司龄" prop="companyAge">
                    <!-- 现在的时间减去入职时间等于司龄 -->
                    <el-input
                      v-model="formData.companyAge"
                      disabled
                      placeholder="距入职时间至今"
                    />
                  </el-form-item>
                </td>
              </tr>
            </table>
          </el-form>
          <div class="ts">
            请上传
            <strong>身份证正反面</strong>
            附件
          </div>
          <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
        </el-tab-pane>
        <el-tab-pane label="合同信息" name="second">
          <contract-information
            ref="ContractInformationEdit"
            @edit-complete="completeContract"
          />
        </el-tab-pane>
        <el-tab-pane label="学历信息" name="third">
          <school-information
            ref="SchoolInformationEdit"
            @edit-complete="completeSchool"
          />
        </el-tab-pane>
        <el-tab-pane label="证书信息" name="four">
          <certificate-information
            ref="CertificateInformationEdit"
            @edit-complete="completeCertificate"
          />
        </el-tab-pane>
        <el-tab-pane label="工资卡信息" name="five">
          <salary-card-information ref="SalaryCardInformationEdit" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="saveAndContinue">
        {{ activeName === 'five' ? '确认' : '保存，下一步' }}
      </el-button>
    </template>
    <!-- 引用 userCopyPopup 组件 -->
    <!-- <userCopyPopup ref="userCopyPopupRef" @callBackCopyData="callBackCopyData" /> -->
  </el-dialog>
</template>
<script>
  // import { mapGetters } from 'vuex'
  import { saveData, getUserIdList } from '@/api/staffManagement/rosterApi'
  import { genUUID } from '@/utils/th_utils.js'
  import { getDictList } from '@/api/system/dict-api' //数据字典api
  import ContractInformation from './ContractInformation.vue' //合同信息组件
  import SchoolInformation from './SchoolInformation.vue' //学历信息组件
  import CertificateInformation from './CertificateInformation.vue' //证书组件
  import SalaryCardInformation from './SalaryCardInformation.vue' //工资卡组件
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'

  export default {
    name: 'RosterEdit',
    components: {
      UploadLargeFileFdfsPopup,
      ContractInformation,
      SchoolInformation,
      CertificateInformation,
      SalaryCardInformation,
    },
    data() {
      const validateIdCard = (rule, value, callback) => {
        // 18位身份证号码正则表达式
        const idCardPattern =
          /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/
        if (!idCardPattern.test(value)) {
          callback(new Error('身份证号填写有误！'))
        }
        // 校验码计算
        const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        const checkCodes = [
          '1',
          '0',
          'X',
          '9',
          '8',
          '7',
          '6',
          '5',
          '4',
          '3',
          '2',
        ]

        const digits = value
          .slice(0, 17)
          .split('')
          .map((digit) => parseInt(digit, 10))
        const sum = digits.reduce(
          (acc, digit, index) => acc + digit * weights[index],
          0
        )
        const checkIndex = sum % 11
        const checkCode = checkCodes[checkIndex]

        // console.log(checkCode, 'checkCode')
        if (value.length === 18 && checkCode === value[17].toUpperCase()) {
          // 计算年龄
          // 提取出生日期
          const birthYear = parseInt(value.substring(6, 10), 10)
          const birthMonth = parseInt(value.substring(10, 12), 10) - 1 // 月份从0开始
          const birthDay = parseInt(value.substring(12, 14), 10)
          const birthDate = new Date(birthYear, birthMonth, birthDay)
          // 获取当前日期
          const today = new Date()
          this.formData.age = today.getFullYear() - birthDate.getFullYear()

          callback()
        } else {
          callback(new Error('身份证号填写有误！'))
        }
      }
      return {
        activeName: 'first',
        dialogFormVisible: false,
        formLoading: false,
        // 基本信息
        formData: {
          sort: 1,
          userName: '',
          userId: '',
          age: '',
          email: '',
          genderName: '',
          telephone: '',
          departName: '',
          companyAge: '',
          onBoardTime: '',
        },
        actionMap: {
          update: '编辑花名册',
          create: '新增花名册',
        },
        dialogStatus: '',
        projectDataList: [],
        departTreeData: [],
        existingUsers: [],
        employeeStatusList: [],
        employeeTypeList: [], //薪资类型
        payScaleList: [], // 薪资等级
        payScaleDisabled: true,
        payMoneyDisabled: true,
        contractTypeList: [], //合同类型_数据字典
        rankList: [], //职级
        titleProfessionalPostList: [], //职称
        degreeTypeList: [], //学历
        rules: {
          userId: [
            { required: true, message: '员工姓名为必填', trigger: 'change' },
          ],
          idCard: [
            { required: true, message: '身份证号不能为空', trigger: 'blur' },
            { validator: validateIdCard, trigger: 'blur' },
          ],
          jobRank: [
            { required: true, message: '职级为必填', trigger: 'change' },
          ],
          employeeStatus: [
            { required: true, message: '员工状态为必填', trigger: 'change' },
          ],
          employeeType: [
            { required: true, message: '薪资类型为必填', trigger: 'change' },
          ],
          onBoardTime: [
            { required: true, message: '入职时间为必填', trigger: 'change' },
          ],
          payMoney: [
            {
              required: true,
              message: '薪资金额为必填',
              trigger: ['blur', 'change'],
            },
          ],
        },
      }
    },
    computed: {
      age() {
        if (this.formData.idCard && this.formData.idCard.length === 18) {
          const value = this.formData.idCard
          // 提取出生日期
          const birthYear = parseInt(value.substring(6, 10), 10)
          const birthMonth = parseInt(value.substring(10, 12), 10) - 1 // 月份从0开始
          const birthDay = parseInt(value.substring(12, 14), 10)
          const birthDate = new Date(birthYear, birthMonth, birthDay)
          // 获取当前日期
          const today = new Date()
          let age = today.getFullYear() - birthDate.getFullYear()
          // 检查月份和日期以调整年龄
          const monthDifference = today.getMonth() - birthDate.getMonth()
          if (
            monthDifference < 0 ||
            (monthDifference === 0 && today.getDate() < birthDate.getDate())
          ) {
            age--
          }
          return age
        }
        return '自动关联身份证' // 默认返回值，可以是 null 或 0，根据你的需求
      },
    },
    watch: {
      //监听 触发入职日期，司龄立即自动更新
      'formData.onBoardTime'(newVal) {
        this.getSurname(newVal)
      },
    },
    created() {
      // this.getDepartList()
    },
    methods: {
      async handleUserInfoUpdate(userInfo) {
        // console.log(userInfo,'userInfo')
        if (userInfo.length > 0) {
          const selectedUser = userInfo[0] //单选
          // 通过id判定是否已经存在
          for (let i = 0; i < this.existingUsers.length; i++) {
            if (selectedUser.id === this.existingUsers[i]) {
              this.$baseMessage(
                '该用户已存在，不允许重复新增',
                'error',
                'vab-hey-message-error'
              )
              this.formData.userId = ''
              // this.$refs.userPopup.showUserDialog({isMultiSelect: true})
              return
            }
          }
          // 更新formData
          this.formData.email = selectedUser.email ? selectedUser.email : ''
          this.formData.telephone = selectedUser.telephone
            ? selectedUser.telephone
            : ''
          this.formData.genderName = selectedUser.genderName
            ? selectedUser.genderName
            : ''
          this.formData.departName =
            selectedUser.departList && selectedUser.departList.length > 0
              ? selectedUser.departList
                  .map((depart) => depart.departName)
                  .join(', ')
              : ''
        }
      },
      getSurname(onBoardTime) {
        if (onBoardTime) {
          const onBoardDate = new Date(onBoardTime)
          const currentDate = new Date()
          let years = currentDate.getFullYear() - onBoardDate.getFullYear()
          let months = currentDate.getMonth() - onBoardDate.getMonth()
          let days = currentDate.getDate() - onBoardDate.getDate()

          // Adjust days and months if needed
          if (days < 0) {
            months -= 1
            days += new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              0
            ).getDate()
          }
          if (months < 0) {
            years -= 1
            months += 12
          }

          // Build the result string based on non-zero values
          let result = ''
          if (years > 0) {
            result += `${years}年`
          }
          if (months > 0) {
            result += `${months}个月`
          }
          if (days > 0) {
            result += `${days}天`
          }

          // If all values are zero, display as '0天'
          if (result === '') {
            result = '0天'
          }

          this.formData.companyAge = result
        } else {
          this.formData.companyAge = ''
        }
      },
      getUserIdList() {
        getUserIdList({}).then((response) => {
          this.existingUsers = response.result // 假设response.result是一个包含所有用户信息的数组
        })
      },
      // getDepartList(){
      //     getDepartList({}).then(response => {
      //       this.departTreeData=response.result
      //     })
      // },
      // 获取多个数据字典
      async getDictDetailsByCode() {
        await getDictList({
          dictCode:
            'employeeStatus,contractType,rankInformation,jobTitle,degreeType,salaryLeave',
        }).then((response) => {
          response.result.forEach((item) => {
            if (item.dictCode === 'employeeStatus') {
              const employeeStatusList = item.children
              this.employeeStatusList = employeeStatusList.map((item) => {
                return {
                  dictName: item.dictName,
                  id: item.id,
                  disabled:
                    item.dictName === '离职',
                }
              })
            }
            if (item.dictCode === 'contractType') {
              this.contractTypeList = item.children
            }
            if (item.dictCode === 'rankInformation') {
              this.rankList = item.children
            }
            if (item.dictCode === 'jobTitle') {
              this.titleProfessionalPostList = item.children
            }
            if (item.dictCode === 'degreeType') {
              this.degreeTypeList = item.children
            }
            if (item.dictCode === 'salaryLeave') {
              this.employeeTypeList = item.children
            }
          })
        })
      },
      // 切换薪资类型，获取薪资等级列表
      changeEmployeeType(val) {
        // 切换薪资类型，重置薪资等级、薪资数值
        this.formData.payScale = ''
        this.formData.payMoney = ''
        const employeeType = this.employeeTypeList.find(
          (item) => item.id === val
        )
        if (employeeType) {
          // employeeType.children为空的时候（表示没有薪资等级），禁用薪资等级选择,解禁薪资数值的输入
          this.payScaleDisabled = employeeType.children.length <= 0
          this.payMoneyDisabled = employeeType.children.length > 0
          // 薪资等级列表
          this.payScaleList = employeeType.children || []
        }
      },
      // 切换薪资等级，获取具体的薪资数值
      changePayScale(val) {
        // 重置薪资数值
        this.formData.payMoney = ''
        const payScale = this.payScaleList.find((item) => item.id === val)
        if (payScale) {
          this.formData.payMoney = payScale.remark || ''
        }
      },
      // 切换tab
      handleTabClick(tab) {
        switch (tab.name) {
          case 'first':
            break
          case 'second':
            this.$refs.ContractInformationEdit.showEdit(this.formData.id)
            break
          case 'third':
            this.$refs.SchoolInformationEdit.showEdit(this.formData.id)
            break
          case 'four':
            this.$refs.CertificateInformationEdit.showEdit(this.formData.id)
            break
          case 'five':
            this.$refs.SalaryCardInformationEdit.showSalaryCardTable(
              this.formData.id
            )
            break
        }
      },
      // 合同信息保存成功，跳转下一tab
      completeContract() {
        this.activeName = 'third'
        this.$refs.SchoolInformationEdit.showEdit(this.formData.id)
      },
      // 学历信息保存成功,跳转下一tab
      completeSchool() {
        this.activeName = 'four'
        this.$refs.CertificateInformationEdit.showEdit(this.formData.id)
      },
      // 证件信息保存成功,跳转下一tab
      completeCertificate() {
        this.activeName = 'five'
      },
      // 工资卡信息保存成功,跳转下一tab
      // completeSalary() {
      // },
      // 切换标签之前的钩子
      beforeLeave() {
        if (!this.formData.userName && this.formData.isAdd === true) {
          this.$message({
            message: '请先保存基本信息',
            type: 'warning',
          })
          return false
        } else {
          return true
        }
      },
      async showEdit(row, sort) {
        await this.getDictDetailsByCode()
        if (!row) {
          this.payScaleDisabled = true
          this.payMoneyDisabled = true
          this.dialogStatus = 'create'
          this.getUserIdList()
          this.initForm(sort)
        } else {
          this.dialogStatus = 'update'
          this.formData = { ...row, isAdd: false }
          const employeeType = this.employeeTypeList.find(
            (item) => item.id === this.formData.employeeType
          )
          // employeeType.children为空的时候（表示没有薪资等级），禁用薪资等级选择,解禁薪资数值的输入
          this.payScaleDisabled = employeeType.children.length <= 0
          this.payMoneyDisabled = employeeType.children.length > 0
          // 薪资等级列表
          this.payScaleList = employeeType.children
          //根据row.departList获取部门名称
          this.formData.departName = this.getTypeNameByDepartList(
            row.departList
          )
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'rosterBaseInfo',
          })
          this.$refs['dataForm'].clearValidate()
        })
      },
      getTypeNameByDepartList(departList) {
        if (!departList) return ''
        // console.log(departList)
        const nameData = []
        departList.forEach((item) => {
          nameData.push(item.departName)
        })
        return nameData.join('、')
      },
      // 初始化表单
      initForm(sort) {
        this.formData = {
          id: genUUID(),
          idCard: '',
          userId: '',
          employeeStatus: '',
          onBoardTime: '',
          jobRank: '',
          titleProfessionalPost: '',
          createTime: '',
          updateTime: '',
          createBy: '',
          updateBy: '',
          employeeType: '',
          sort: sort,
          remarks: '',
          payScale: '', // 薪资等级
          payMoney: '', // 薪资
          isAdd: true,
        }
      },
      // 保存下一步
      saveAndContinue() {
        switch (this.activeName) {
          case 'first':
            this.save()
            break
          case 'second':
            this.$refs.ContractInformationEdit.save()
            //走了合同信息 保存方法
            break
          case 'third':
            this.$refs.SchoolInformationEdit.save()
            break
          case 'four':
            this.activeName = 'five'
            this.$refs.SalaryCardInformationEdit.showSalaryCardTable(
              this.formData.id
            )
            break
          case 'five':
            this.close()
            break
        }
      },
      async save() {
        const valid = await this.$refs['dataForm'].validate()
        if (valid) {
          try {
            let data = { ...this.formData }
            await saveData(data)
            this.$baseMessage(
              this.formData.isAdd ? '保存基本信息成功！' : '修改成功!',
              'success',
              'vab-hey-message-success'
            )
            this.formData.isAdd = false
            this.activeName = 'second'
            await this.$refs.ContractInformationEdit.showEdit(data.id)
            this.$emit('fetch-data')
          } catch (err) {
            this.$baseMessage(
              this.formData.isAdd ? '保存基本信息失败！' : '修改基本信息失败!',
              'error',
              'vab-hey-message-error'
            )
          }
        }
      },
      close() {
        if (this.activeName === 'five') {
          this.$emit('fetch-data')
        }
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
        this.activeName = 'first'
      },
    },
  }
</script>
<style>
  .ts {
    margin: 20px 0;
    font-size: 16px;
    strong {
      color: #ff0909;
    }
  }
</style>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
  .statistics {
    width: 100%;
    font-size: 18px;
    margin: 10px 0;
    display: flex;
    justify-content: space-around;
    .item {
      display: flex;
      justify-content: space-between;
      .number {
        color: #e53f3f;
      }
    }
  }
</style>
