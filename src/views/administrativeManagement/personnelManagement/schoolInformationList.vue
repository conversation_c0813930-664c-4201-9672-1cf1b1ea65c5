<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="所属部门" prop="departId">
            <t-form-tree
              v-model="queryForm.departId"
              :default-props="{ children: 'children', label: 'departName' }"
              :show-top="false"
              :tree-data="departTreeData"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="userName">
            <el-input
              v-model="queryForm.userName"
              placeholder="请输入姓名"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="毕业院校" prop="graduateSchool">
            <el-input
              v-model="queryForm.graduateSchool"
              placeholder="请输入毕业院校"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="学历" prop="eduBackground">
            <el-select
              v-model="queryForm.eduBackground"
              clearable
              placeholder="请选择学历"
              style="width: 200px"
            >
              <el-option
                v-for="item in eduBackgroundList"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="!fold" label="专业" prop="profession">
            <el-input
              v-model="queryForm.profession"
              placeholder="请输入专业"
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
            <el-button
              style="margin: 0 0 0 10px !important"
              type="text"
              @click="handleFold"
            >
              <span v-if="fold">展开</span>
              <span v-else>合并</span>
              <vab-icon
                class="vab-dropdown"
                :class="{ 'vab-dropdown-active': fold }"
                icon="arrow-up-s-line"
              />
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          :disabled="exportLoading"
          icon="el-icon-download"
          type="warning"
          @click.native="exportBtn('学历信息.xlsx')"
        >
          导出
        </el-button>
        <!--        <el-button icon="el-icon-delete" type="danger" @click="batchDelete" v-permissions="{permission:['roster:del']}">-->
        <!--          批删-->
        <!--        </el-button>-->
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.prop === 'graduationTime'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <!-- <span v-else-if="item.label === '部门'">
            {{ getTypeNameByDepartList(row[item.prop]) }}
          </span> -->
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['schoolInformationList:edit'] }"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <!--          <el-button-->
          <!--            style="margin: 0 10px 10px 0 !important"-->
          <!--            type="danger"-->
          <!--            icon="el-icon-delete"-->
          <!--            @click="handleDelete(row)"-->
          <!--            v-permissions="{permission:['schoolInformationList:del']}"-->
          <!--          >删除</el-button>-->
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-detail ref="tableDetail" />
    <!--    <table-edit ref="tableEdit" @fetch-data="fetchData" />-->
    <table-edit ref="SchoolInformationEdit" @edit-complete="fetchData" />
    <VabTableExport ref="tableExport" @refreshPage="resetFormPage" />
  </div>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import tableDetail from './components/schoolInforDetail.vue'
  import { getDictList } from '@/api/system/dict-api' //数据字典api
  import {
    getDataListByPageEdu,
    deleteDataEdu,
    exportDataSchoolInfo,
  } from '@/api/staffManagement/rosterApi'
  import { getDepartList } from '@/api/system/depart-api'
  import tableEdit from './components/SchoolInfoDialog.vue' //学历信息编辑组件
  export default {
    name: 'SchoolInformationList',
    components: {
      tableDetail,
      tableEdit,
    },
    mixins: [tableMix],
    data() {
      return {
        formMaxHeight: 2,
        columns: [
          {
            label: '姓名',
            prop: 'userName',
            width: '120',
          },
          {
            label: '部门',
            prop: 'departName',
          },
          {
            label: '职级',
            prop: 'jobRankName',
          },

          {
            label: '毕业院校',
            prop: 'graduateSchool',
          },
          {
            label: '学历',
            prop: 'eduBackgroundName',
            width: '120',
          },
          {
            label: '毕业时间',
            prop: 'graduationTime',
            width: '120',
          },
          {
            label: '专业',
            prop: 'profession',
          },
        ],
        queryForm: {
          eduBackground: '',
          profession: '',
          graduateSchool: '',
          userName: '',
          departId: '',
          employeeId: '',
        },
        eduBackgroundList: [],
        rankList: [],
        departTreeData: [],
        departName: [],
        userList: [],
      }
    },
    computed: {},
    created() {
      this.getDictDetailsByCode() //获取数据字典
      this.getDepartList()
      this.fetchData()
    },
    methods: {
      getDepartList(){
        getDepartList({}).then(response => {
          //获取所属部门数据
          this.departTreeData=response.result
        })
      },
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportDataSchoolInfo({ ...this.queryForm, ...columns }).then(
          (response) => {
            const link = document.createElement('a')
            link.download = this.queryForm.excelName
            link.href = window.URL.createObjectURL(new Blob([response]))
            document.body.appendChild(link)
            link.click()
            link.download = ''
            document.body.removeChild(link)
            URL.revokeObjectURL(response)
          }
        )
        this.exportLoading = false
      },
      // 获取多个数据字典
      getDictDetailsByCode() {
        getDictList({ dictCode: 'degreeType' }).then((response) => {
          response.result.forEach((item) => {
            if (item.dictCode === 'degreeType') {
              this.eduBackgroundList = item.children
            }
          })
        })
      },
      // 获取部门名称
      getTypeNameByDepartList(departList) {
        if (!departList) return ''
        const nameData = []
        departList.forEach((item) => {
          nameData.push(item.departName)
        })
        return nameData.join('、')
      },
      async fetchData() {
        this.listLoading = true
        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        await getDataListByPageEdu(queryForm).then((res) => {
          const {
            result: { total, records },
          } = res
          this.list = records
          this.listLoading = false
          this.pageInfo.total = Number(total)
        })
      },

      handleEdit(row) {
        this.$refs['SchoolInformationEdit'].showEdit(row)
      },
      // handleAdd() {
      //   this.$refs['tableEdit'].showEdit()
      // },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除吗', null, async () => {
            deleteDataEdu({ id: row.id })
              .then(() => {
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      // 批删除
      batchDelete() {
        if (this.selectRows.length === 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
          return
        }
        const idArr = this.selectRows.map((item) => item.id).join(',')
        this.$baseConfirm('你确定要删除所有选中吗', null, async () => {
          deleteDataEdu({ id: idArr })
            .then(() => {
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
