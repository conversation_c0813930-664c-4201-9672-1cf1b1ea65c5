<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="closeBtn"
    center
    :close-on-click-modal="false"
    :title="pageTitle"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
      :rules="rules"
    >
      <table class="form-table">
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                style="width: 250px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请人" prop="createBy">
              <el-input
                v-model="formData.createByName"
                disabled
                style="width: 250px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请日期" prop="applicationDate">
              <el-date-picker
                v-model="formData.applicationDate"
                placeholder="请选择申请日期"
                style="width: 250px"
                type="date"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="所属部门" prop="depart">
              <t-form-tree
                v-if="dialogFormVisible"
                v-model="formData.depart"
                :default-props="{ children: 'children', label: 'departName' }"
                :disabled="!formData.isAdd || formData.projectId !== ''"
                placeholder="请选择所属部门"
                :show-top="false"
                :tree-data="departTreeData"
                @change="changeDepart"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="费用所属项目" prop="projectId">
              <el-input
                v-model="formData.projectCode"
                :disabled="!formData.isAdd"
                placeholder="选择所属项目"
                style="width: 250px"
                @click.native="handleProject"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="formData.projectName"
                disabled
                placeholder="自动关联所属项目"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="用工事由" prop="recruitAndUse">
              <el-input
                v-model="formData.recruitAndUse"
                maxlength="1000"
                placeholder="请输入用工事由"
                rows="3"
                style="width: 1150px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                format="yyyy-MM-dd HH:mm"
                placeholder="请选择开始时间"
                style="width: 250px"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="changeDate"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                format="yyyy-MM-dd HH:mm"
                placeholder="请选择结束时间"
                style="width: 250px"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="changeDate"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="时长(天)" prop="duration">
              <el-input-number
                v-model="formData.duration"
                controls-position="right"
                :max="formData.durationMax"
                :min="0.5"
                :precision="1"
                :step="0.5"
                :step-strictly="true"
                style="width: 250px"
                @change="changePrice"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="用工人数(个)" prop="numberOfWorkers">
              <el-input-number
                v-model="formData.numberOfWorkers"
                controls-position="right"
                :min="0"
                :precision="0"
                :step="1"
                style="width: 250px"
                @change="changePrice"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="用工单价(元/天)" prop="unitPrice">
              <el-input-number
                v-model="formData.unitPrice"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="1"
                style="width: 250px"
                @change="changePrice"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="用工金额(元)" prop="totalAmount">
              <el-input
                v-model.number="formData.totalAmount"
                disabled
                placeholder="自动计算"
                style="width: 250px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                maxlength="1000"
                placeholder="请输入备注"
                rows="3"
                style="width: 1150px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </div>
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { parseTime, genUUID } from '@/utils/th_utils'
  import {
    saveData,
    getDuration,
  } from '@/api/administrativeManagement/temporaryEmployment-api'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'

  export default {
    computed: {
      ...mapGetters({
        departList: 'user/departList',
        userName: 'user/userName',
        userId: 'user/userId',
        // 所有的部门列表数据
        allDepartList: 'acl/allDepartList',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增' : '编辑'
      },
    },
    components: {
      ProjectListPopup,
      UploadLargeFileFdfsPopup,
    },
    props: {
      departTreeData: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
      const validateDate = (rule, value, callback) => {
        let startDate = Date.parse(this.formData.startDate)
        let endDate = Date.parse(this.formData.endDate)
        if (endDate && endDate && startDate > endDate) {
          this.formData.duration = '自动计算'
          callback(new Error('结束时间不能小于开始时间，请修改!'))
        }
        callback()
      }
      return {
        dialogFormVisible: false,
        formLoading: false,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        formData: {
          id: '',
          serialNumber: '',
          createDepart: '',
          createDepartName: '',
          applicationDate: '',
          depart: '',
          departName: '',
          departCode: '',
          createBy: '',
          createByName: '',
          viewUser: '',
          operationCode: 'LSYG',
          serialNumberTable: 'temporary_employment',
          recruitAndUse: '', //用工事由
          startDate: '',
          endDate: '',
          duration: 1,
          durationMax: 1,
          durationStr: '',
          numberOfWorkers: 1, //用工人数
          unitPrice: 0, //用工单价(元/天)
          totalAmount: null, //用工金额(元)
          remark: '',
        },
        rules: {
          applicationDate: [
            { required: true, message: '请选择申请日期', trigger: 'change' },
          ],
          startDate: [
            { required: true, message: '请选择开始时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
          endDate: [
            { required: true, message: '请选择结束时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
          numberOfWorkers: [
            { required: true, message: '请输入用工人数', trigger: 'blur' },
          ],
          unitPrice: [
            { required: true, message: '请输入用工单价', trigger: 'blur' },
          ],
          depart: [
            {
              required: true,
              message: '请选择所属部门',
              trigger: 'change',
            },
          ],
          recruitAndUse: [
            { required: true, message: '请输入用工事由', trigger: 'blur' },
          ],
        },
      }
    },
    methods: {
      async showDialog(row) {
        if (row.isAdd) {
          this.init()
        } else {
          this.formData = { ...row }
          // this.changeDate()
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      init() {
        this.formData = {
          id: genUUID(),
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          applicationDate: parseTime(new Date()),
          projectId: '',
          depart: this.departList[0].id,
          departName: this.departList[0].departName,
          departCode: this.departList[0].departCode,
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'LSYG',
          serialNumberTable: 'temporary_employment',
          recruitAndUse: '', //用工事由
          startDate: '',
          endDate: '',
          duration: 1, //时长
          durationMax: 1, //时长
          durationStr: '', //时长
          numberOfWorkers: 1, //用工人数
          unitPrice: 0, //用工单价(元/天)
          totalAmount: null, //用工金额(元)
          remark: '',
          isAdd: true,
        }
      },
      closeBtn() {
        if (this.formData.isAdd) {
          this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        }
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
        this.init()
      },
      saveBtn() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
      changeDepart(id) {
        for (const item of this.departTreeData) {
          if (item.id === id) {
            this.formData.depart = item.id
            this.formData.departCode = item.departCode
            this.formData.departName = item.departName
          }
        }
      },
      handleProject() {
        const projectStatusList = [
          '348ac237-1cb0-4b44-85fa-961bfb4465e9',
          '80042b9f-72bd-4249-b321-48f2dfe4db2b',
          'c2302242-8143-4ad6-876f-1d38785e1f44',
        ]
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName,
          queryParams: { projectStatusList: projectStatusList },
        })
      },
      getProjectInfo(data) {
        console.log(data, 'data')
        if (data.length === 0) {
          this.formData = {
            ...this.formData,
            projectId: '',
            projectName: '',
            projectCode: '',
            depart: this.departList[0].id,
            flowDepart: this.departList[0].id,
            departCode: this.departList[0].departCode,
            departName: this.departList[0].departName,
          }
        }
        if (data.length > 0 && data[0].id !== this.formData.projectId) {
          const dataObj = data[0]
          const {
            id,
            projectName,
            serialNumber,
            depart,
            departCode,
            departName,
          } = dataObj
          this.formData = {
            ...this.formData,
            projectId: id,
            projectName,
            projectCode: serialNumber,
            depart: depart,
            flowDepart: depart,
            departCode: departCode,
            departName: departName,
          }
        }
      },
      changeDate() {
        this.formData.durationStr = '自动计算'
        this.formData.totalAmount = null
        if (this.formData.startDate && this.formData.endDate) {
          getDuration({
            startDate: this.formData.startDate,
            endDate: this.formData.endDate,
          })
            .then((res) => {
              this.formData.duration = res.result
              this.formData.durationMax = res.result
              this.formData.durationStr = res.result + '天'
            })
            .catch((error) => {
              this.formData.durationMax = 1
              this.formData.durationStr = '自动计算'
              console.log(error)
            })
            .finally(() => {
              this.calculateTotalAmount()
            })
        }
      },

      changePrice() {
        // 先校验开始和结束日期
        if (!this.formData.startDate || !this.formData.endDate) {
          this.$baseMessage(
            '请先选择开始和结束时间',
            'warning',
            'vab-hey-message-warning'
          )
          return
        }
        this.formData.totalAmount = null

        // 日期校验通过后计算总金额
        this.calculateTotalAmount()
      },

      calculateTotalAmount() {
        if (
          this.formData.numberOfWorkers !== undefined &&
          this.formData.unitPrice !== undefined &&
          this.formData.duration
        ) {
          this.formData.totalAmount = (
            this.formData.duration *
            this.formData.numberOfWorkers *
            this.formData.unitPrice
          ).toFixed(2)
        }
        this.$forceUpdate()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image-upload {
    position: absolute;
    right: 21px;
    bottom: 93px;
    width: 536px;
    height: 160px;
    background: #fff;
    border-left: 1px solid #bbb;
  }
</style>
