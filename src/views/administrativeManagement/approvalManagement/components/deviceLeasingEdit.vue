<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
      :rules="rules"
    >
      <table v-loading="formloading" class="form-table">
        <tr class="title">
          <td colspan="3">基本信息</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                readonly
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请人" prop="createByName">
              <el-input
                v-model="formData.createByName"
                disabled
                readonly
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请日期" prop="applicationDate">
              <el-date-picker
                v-model="formData.applicationDate"
                disabled
                format="yyyy-MM-dd"
                placeholder="选择时间"
                readonly
                style="width: 280px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="所属项目" prop="projectName">
              <el-input
                v-model="formData.projectName"
                :disabled="!formData.isAdd"
                placeholder="请选择"
                style="width: 280px"
                @click.native="selectContractProject"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="费用所属部门" prop="departName">
              <el-input
                v-model="formData.departName"
                disabled
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                format="yyyy-MM-dd HH:mm"
                placeholder="选择时间"
                style="width: 280px"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
                @change="timewstamp"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                format="yyyy-MM-dd HH:mm"
                placeholder="选择时间"
                style="width: 280px"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
                @change="timewstamp"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="时长(天    )" prop="duration">
              <el-input
                v-model="formData.duration"
                placeholder="请输入时长，例如：3天"
                style="width: 280px"
                @change="calculateAmount"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="租赁数量" prop="leasingCount">
              <el-input-number
                v-model="formData.leasingCount"
                controls-position="right"
                :min="1"
                placeholder="请输入租赁数量"
                style="width: 280px"
                @change="calculateAmount"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="租赁单价" prop="leasingPrice">
              <el-input-number
                v-model="formData.leasingPrice"
                controls-position="right"
                :min="0"
                placeholder="请输入租赁单价"
                :precision="2"
                style="width: 280px"
                @change="calculateAmount"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="租赁金额" prop="leasingAmount">
              <el-input
                v-model="formData.leasingAmount"
                disabled
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label=" 上传照片">
              <div class="form-item-container">
                <!-- <div class="item-top">
                  <span>可添加图片或直接上传图片，上传图片大小不超过10M</span>
                </div> -->
                <div class="item-radio-btn" style="margin-top: 10px">
                  <el-button icon="el-icon-upload2" @click="showUploadItem()">
                    上传照片
                  </el-button>
                </div>
                <div v-if="formData.picture" class="select-list">
                  <div class="select-item">
                    <el-image
                      :preview-src-list="[formData.picture]"
                      :src="formData.picture"
                      style="width: 40px; height: 40px"
                    />
                    <i
                      class="el-icon-delete"
                      @click="formData.picture = ''"
                    ></i>
                  </div>
                </div>
              </div>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="formData.remarks"
                placeholder="请输入备注"
                :rows="3"
                style="width: 1180px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="租赁事由" prop="leasingReason">
              <el-input
                v-model="formData.leasingReason"
                placeholder="请输入租赁事由"
                :rows="3"
                style="width: 1180px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <!-- 附件上传 -->
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <UploadItem ref="uploadItemRef" @callbackSuccess="callbackSuccess" />
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
  </el-dialog>
</template>

<script>
  import { saveData } from '@/api/administrativeManagement/deviceLeasing-api'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import { getDictList } from '@/api/system/dict-api'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import { mapGetters } from 'vuex'
  import UploadItem from '../../../administrativeManagement/announcementManagement/UploadItem.vue'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'

  export default {
    name: 'TableEdit',
    components: {
      ProjectListPopup,
      UploadItem,
      UploadLargeFileFdfsPopup,
    },
    props: {},
    data() {
      const validateDate = (rule, value, callback) => {
        let startDate = Date.parse(this.formData.startTime)
        let endDate = Date.parse(this.formData.endTime)
        if (endDate && endDate && startDate > endDate) {
          this.formData.duration = '自动计算'
          callback(new Error('结束时间不能小于开始时间，请修改!'))
        }
        callback()
      }
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        applicationDate: '',
        createDepartName: '',
        projectList: [],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        rules: {
          projectName: {
            required: true,
            message: '费用所属项目为必填',
            trigger: 'change',
          },
          startTime: [
            { required: true, message: '请选择开始时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
          endTime: [
            { required: true, message: '请选择结束时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
        },
        queryForm: {},
        duration: '',
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
    },
    created() {
      this.getDictDetails()
    },
    methods: {
      async getProjectInfo(data) {
        console.log(data, 'data')
        if (data.length > 0) {
          const dataObj = data[0]
          const { id, projectName, serialNumber } = dataObj
          if (this.formData.budgetId && id !== this.formData.budgetId) {
            this.formData.budgetId = ''
            this.detailList = []
          }
          this.formData = {
            ...this.formData,
            projectId: id,
            projectName,
            projectSerialNumber: serialNumber,
            depart: data[0].depart, //部门id
            departName: data[0].departName, //部门名字
          }
          console.log(this.formData, 'formData')
          console.log(projectName, 'projectName')
          this.$refs.dataForm.clearValidate('projectName')
        }
      },
      selectContractProject() {
        this.projectStatusList = [
          '348ac237-1cb0-4b44-85fa-961bfb4465e9',
          '80042b9f-72bd-4249-b321-48f2dfe4db2b',
          'c2302242-8143-4ad6-876f-1d38785e1f44',
        ]
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName,
          queryParams: { projectStatusList: this.projectStatusList },
        })
      },
      showUploadItem() {
        const data = {
          bizId: this.formData.id,
          bizCode: 'UploadStart',
          isOneFile: true,
        }
        this.$refs.uploadItemRef.showUploadFileDialog(data)
      },
      timewstamp() {
        // 不再自动计算时长，由用户手动输入
        // 如果需要验证开始时间和结束时间的合理性，可以保留这部分逻辑
        if (this.formData.startTime && this.formData.endTime) {
          var stime = Date.parse(new Date(this.formData.startTime))
          var etime = Date.parse(new Date(this.formData.endTime))
          // 验证时间的合理性
          if (stime > etime) {
            this.$message.error('结束时间不能小于开始时间，请修改!')
          }
        }
      },
      mileageCalculation() {
        if (this.formData.startForKm && this.formData.afterForKm) {
          var skm = this.formData.startForKm
          var ekm = this.formData.afterForKm
          // 计算使用的公里数
          var usedKm = ekm - skm
          // 格式化到小数点后两位（如果需要）
          var formattedKm = usedKm.toFixed(1)
          // 将结果与 'Km' 拼接
          var time = formattedKm + 'Km'
          this.formData.totalKm = time
        }
      },
      showEdit(row) {
        console.log(row, 'row')
        if (!row) {
          this.title = '新增临时租赁申请'
          this.dialogStatus = 'create'
          this.createDepartName = this.departList[0].departName
          this.initForm()
        } else {
          this.title = '编辑临时租赁申请'
          this.dialogStatus = 'update'
          this.dialogFormVisible = true
          this.formData = Object.assign({ isAdd: false }, row)
          this.formData.updateBy = this.userName
        }
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'deviceLeasing',
          })
        })
        this.dialogFormVisible = true
        //        this.getProjectTree()
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      getDictDetails() {
        getDictList({ dictCode: 'travelMode' }).then((response) => {
          const selectedTavelModeName = response.result[0].children
          this.selectedTavelModeName = selectedTavelModeName.map((item) => {
            return {
              id: item.id,
              dictName: item.dictName,
              disabled: item.dictName === '私车公用' ? true : false,
            }
          })
        }),
          getDictList({ dictCode: 'getAccommodation' }).then((response) => {
            this.selectedAccommodation = response.result[0].children
          })
      },
      initForm() {
        this.formData = {
          id: genUUID(),
          leasingReason: '', //事由
          updateBy: '',
          depart: this.departList[0].id, //部门id
          createDepart: this.departList[0].id, //部门id
          operationCode: 'SBZL',
          serialNumberTable: 'device_leasing',
          departList: this.departList,
          leasingCount: '', //租赁数量
          startTime: '', //开始时间
          departName: '', //部门名字
          serialNumber: '', //序号
          departCode: this.departList[0].departCode, //部门code
          viewUser: this.userId, //创建人名字
          createByName: this.userName, //创建人名字
          leasingAmount: '', //租赁金额
          updateTime: '',
          sort: '',
          createBy: this.userId, //创建人ID
          createTime: '',
          endTime: '', //结束时间
          projectId: '', //项目ID
          applicationDate: parseTime(new Date()), //申请日期
          leasingPrice: '', //租赁单价
          remarks: '',
          duration: '', // 改为空字符串，由用户手动输入
          isAdd: true,
        }
        console.log(this.departList[0])
      },
      calculateAmount() {
        if (this.formData.leasingCount && this.formData.leasingPrice) {
          // 从时长中提取数字部分，支持小数形式的天数
          const durationStr = this.formData.duration || ''
          // 使用parseFloat代替parseInt，以支持小数
          const durationMatch = durationStr.match(/([\d.]+)/)
          const durationNum = durationMatch ? parseFloat(durationMatch[1]) : 0

          const amount =
            this.formData.leasingCount *
            this.formData.leasingPrice *
            durationNum
          this.formData.leasingAmount = amount.toFixed(2)
        }
      },
      callbackSuccess(data) {
        console.log('===========')
        console.log(data.urlPath)
        this.formData.picture = data.urlPath
      },
      save() {
        this.$refs['dataForm'].validate(async (valid) => {
          if (valid) {
            console.log(this.formData)
            // return
            saveData(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .form-item-container {
    height: 50px;

    .select-list {
      width: 100%;
      height: 100%;
    }
  }
</style>
