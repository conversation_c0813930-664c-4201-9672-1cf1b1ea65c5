<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
    @close="close"
  >
    <div
      style="
        display: flex;
        max-height: 75vh;
        overflow-y: scroll;
        overflow-x: hidden;
      "
    >
      <div style="flex: 1">
        <el-descriptions
          v-loading="formLoading"
          border
          class="margin-top"
          :column="2"
          size="medium"
          style="margin-bottom: 20px"
        >
          <el-descriptions-item label="单号">
            {{ form.serialNumber }}
          </el-descriptions-item>
          <el-descriptions-item label="所属项目">
            {{ form.projectName }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ form.startTime | dateformat('YYYY-MM-DD HH:mm:ss') }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ form.endTime | dateformat('YYYY-MM-DD HH:mm:ss') }}
          </el-descriptions-item>
          <el-descriptions-item label="时长">
            {{ form.duration }}
          </el-descriptions-item>
          <el-descriptions-item label="租赁数量">
            {{ form.leasingCount }}
          </el-descriptions-item>
          <el-descriptions-item label="租赁单价">
            {{ form.leasingPrice | currencyFormat }}
          </el-descriptions-item>
          <el-descriptions-item label="租赁金额">
            {{ form.leasingAmount | currencyFormat }}
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ form.createByName }}
          </el-descriptions-item>
          <el-descriptions-item label="申请部门">
            {{ form.departName }}
          </el-descriptions-item>
          <el-descriptions-item label="申请日期">
            {{ form.applicationDate | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item label="审批状态">
            <el-tag :type="fmtFlowType">{{ form.approvalResultName }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="租赁事由" :span="2">
            {{ form.leasingReason }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ form.remarks }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 附件上传 -->
        <UploadLargeFileFdfsPopupDetail ref="UploadLargeFileFdfsPopupDetail" />

        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="form.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>

    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="close">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'

  export default {
    name: 'DeviceLeasingDetail',
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    data() {
      return {
        form: {},
        title: '设备租赁详情',
        dialogFormVisible: false,
        formLoading: false,
        isApproval: false,
      }
    },
    computed: {},
    methods: {
      showApprovalFlowByParams(bizKey, row, paramsMap) {
        this.showDetail(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(
            bizKey,
            row,
            paramsMap
          )
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogFormVisible = false
      },
      showDetail(row) {
        this.form = Object.assign({}, row)
        this.dialogFormVisible = true
        this.isApproval = false
        this.$nextTick(() => {
          if (this.form.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.form.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.form.id,
            bizCode: 'deviceLeasing',
            isShow: true,
          })
        })
      },
      close() {
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped></style>
