<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="临时用工详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1680px"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
          <el-tab-pane label="基本信息" name="first">
            <el-form
              ref="dataForm"
              :inline="true"
              label-position="right"
              label-width="130px"
              :model="formData"
            >
              <el-descriptions
                border
                class="margin-top"
                :column="3"
                :label-style="labelStyle"
                size="medium"
              >
                <el-descriptions-item>
                  <template slot="label">单号</template>
                  {{ formData.serialNumber }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">申请人</template>
                  {{ formData.createByName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">申请人部门</template>
                  {{ formData.createDepartName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">申请日期</template>
                  {{ formData.applicationDate | dateformat('YYYY-MM-DD') }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">所属部门</template>
                  {{ formData.departName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">费用所属项目</template>
                  {{ formData.projectName }}
                </el-descriptions-item>
                <el-descriptions-item :span="3">
                  <template slot="label">用工事由</template>
                  {{ formData.recruitAndUse }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">开始时间</template>
                  {{ formData.startDate | dateformat('YYYY-MM-DD HH:mm') }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">结束时间</template>
                  {{ formData.endDate | dateformat('YYYY-MM-DD HH:mm') }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">时长</template>
                  {{ formData.durationStr }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">用工人数(个)</template>
                  {{ formData.numberOfWorkers }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">用工单价(元/天)</template>
                  {{ formData.unitPrice | currencyFormat }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label">用工金额(元)</template>
                  {{ formData.totalAmount | currencyFormat }}
                </el-descriptions-item>
                <el-descriptions-item :span="3">
                  <template slot="label">备注</template>
                  {{ formData.remark }}
                </el-descriptions-item>
              </el-descriptions>
            </el-form>
            <!-- 附件上传 -->
            <UploadLargeFileFdfsPopupDetail
              ref="UploadLargeFileFdfsPopupDetail"
              style="margin-top: 20px"
            />
          </el-tab-pane>
        </el-tabs>

        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'

  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        activeName: 'first',
        isApproval: false,
        labelStyle: {
          width: '130px',
        },
      }
    },
    methods: {
      showApprovalFlowByParams(bizKey, row, params) {
        this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(bizKey, row, params)
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      showDialog(row) {
        this.isApproval = false
        this.activeName = 'first'
        this.formData = { ...row }
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
            isShow: true,
          })
        })
        this.dialogDetailVisible = true
      },
      handleTabClick() {},
    },
  }
</script>

<style lang="scss" scoped></style>
