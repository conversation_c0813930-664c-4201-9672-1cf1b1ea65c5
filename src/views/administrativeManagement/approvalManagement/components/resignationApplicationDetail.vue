<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="离职申请详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1400px"
  >
    <div
      style="
        display: flex;
        max-height: 75vh;
        overflow-y: scroll;
        overflow-x: hidden;
      "
    >
      <div style="flex: 1">
        <el-descriptions
          v-loading="formLoading"
          border
          class="margin-top"
          :column="3"
          size="medium"
          style="margin-bottom: 20px"
        >
          <el-descriptions-item>
            <template slot="label">单号</template>
            {{ formData.serialNumber }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">申请人</template>
            {{ formData.createByName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">申请部门</template>
            {{ formData.createDepartName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">入职日期</template>
            {{ formData.onBoardTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">离职日期</template>
            {{ formData.terminationTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">申请日期</template>
            {{ formData.applyTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">离职原因</template>
            {{ formData.reasonForTermination }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">离职说明</template>
            {{ formData.resignationDescription }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">是否有申请备用金</template>
            {{ formData.isApplyStandbyMoney }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 附件上传 -->
        <UploadLargeFileFdfsPopupDetail
          ref="UploadLargeFileFdfsPopupDetail"
          style="margin-top: 20px"
        />
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail'

  export default {
    name: 'ResignationApplicationDetail',
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    props: {},
    data() {
      return {
        size: '',
        dialogDetailVisible: false,
        formLoading: false,
        formData: {
          id: '',
        },
        isApproval: false,
      }
    },
    created() {},
    mounted() {},
    methods: {
      showApprovalFlowByParams(bizKey, row, paramsMap) {
        this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(
            bizKey,
            row,
            paramsMap
          )
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      showDialog(row) {
        this.isApproval = false
        this.formData = { ...row }
        this.dialogDetailVisible = true
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: 'resignationApplication',
            isShow: true,
          })
        })
      },
    },
  }
</script>
<style lang="scss" scoped></style>
