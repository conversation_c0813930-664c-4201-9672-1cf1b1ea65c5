<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="actionMap[dialogStatus]"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="150px"
      :model="formData"
      :rules="rules"
    >
      <table v-loading="formloading" class="form-table">
        <tr class="title">
          <td colspan="3">基本信息</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请人" prop="createBy">
              <template>
                <el-input v-model="formData.createByName" disabled />
              </template>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请部门" prop="createDepart">
              <el-input v-model="formData.createDepartName" disabled />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="入职日期" prop="onBoardTime">
              <el-date-picker
                v-model="formData.onBoardTime"
                disabled
                placeholder="入职日期"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>

          <td>
            <el-form-item label="离职日期" prop="terminationTime">
              <el-date-picker
                v-model="formData.terminationTime"
                placeholder="离职日期"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请日期" prop="applyTime">
              <el-date-picker
                v-model="formData.applyTime"
                disabled
                placeholder="申请日期"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="离职原因" prop="reasonForTermination">
              <el-radio-group v-model="formData.reasonForTermination">
                <el-radio label="家庭原因">家庭原因</el-radio>
                <el-radio label="个人原因">个人原因</el-radio>
                <el-radio label="发展原因">发展原因</el-radio>
                <el-radio label="其他原因">其他原因</el-radio>
              </el-radio-group>
            </el-form-item>
          </td>
          <td colspan="2">
            <el-form-item label="离职说明" prop="resignationDescription">
              <el-input
                v-model="formData.resignationDescription"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 700px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="是否有申请备用金" prop="isApplyStandbyMoney">
              <el-select v-model="formData.isApplyStandbyMoney">
                <el-option label="是" value="是" />
                <el-option label="否" value="否" />
              </el-select>
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <!-- 附件上传 -->
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="saveData">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { genUUID } from '@/utils/th_utils.js'
  import { saveData,checkData } from '@/api/administrativeManagement/resignationApplication-api'
  import { mapGetters } from 'vuex'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup'
  // 获取花名册列表
  import { getDataList } from '@/api/staffManagement/rosterApi'
  import moment from 'moment/moment'
  export default {
    name: 'TableEdit',
    components: {
      UploadLargeFileFdfsPopup,
    },
    props: {},
    data() {
      return {
        title: '',
        formloading: false,
        visible: false,
        selectLeaveType: [],
        projectList: [],
        departData: [],
        defaultKeys: [],
        dialogStatus: '',
        onBoardTime: '',
        dialogFormVisible: false,
        actionMap: {
          update: '编辑离职申请信息',
          create: '新增离职申请信息',
        },
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        formData: {},
        rules: {
          onBoardTime: {
            required: true,
            message: '入职日期为必填',
            trigger: 'blur',
          },
          isApplyStandbyMoney: {
            required: true,
            message: '是否有申请备用金为必填',
            trigger: 'change',
          },
          terminationTime: {
            required: true,
            message: '离职日期为必填',
            trigger: 'blur',
          },
          resignationDescription: {
            required: true,
            message: '离职说明为必填',
            trigger: 'blur',
          },
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
    },
    created() {},
    methods: {
      async getDataList() {
        await getDataList({}).then((res) => {
          // 通过userId获取对应的onBoardTime 赋值给 onBoardTime
          // 使用find方法遍历查找相同的用户ID
          console.info(res)
          if(res !== undefined){
            const foundUser = res.result.find(
              (user) => user.userId === this.userId
            )
            if (foundUser) {
              // 获取对应的入职日期onBoardTime
              this.onBoardTime = foundUser.onBoardTime
            } else {
              console.log('找不到对应的用户')
            }
          }

        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          isAdd: true,
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          departCode: this.departList[0].departCode,
          serialNumberTable: 'resignation_application',
          applyTime: moment().format('YYYY-MM-DD'), //申请时间
          onBoardTime: this.onBoardTime, //入职日期
          reasonForTermination: '个人原因', //默认选中“个人原因”
          terminationTime: '', //离职日期
          isApplyStandbyMoney: '', //是否有申请备用金
          resignationDescription: '', //离职说明
          remark: '', //备注
          sort: 0,
          createBy: this.userId, //填报人
          createByName: this.userName,
          viewUser: this.userId, //填报人姓名
          createTime: '',
          updateBy: '',
          operationCode: 'LZSQ',
          updateTime: '',
        }
      },
      async showEdit(row) {
        if (!row) {
          this.dialogStatus = 'create'
          await this.getDataList()
          this.initForm()
        } else {
          this.dialogStatus = 'update'
          this.dialogFormVisible = true
          this.formData = Object.assign({ isAdd: false }, row)
          this.formData.updateBy = this.userId
          this.formData.updateTime = new Date()
        }
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'resignationApplication',
          })
        })
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      saveData() {
        this.$refs['dataForm'].validate(async (valid) => {
          if (valid) {
            var checkDataResult = true
            if (this.formData.isAdd) {
              const data = {
                createBy: this.formData.createBy,
              }
             await checkData(data).then((response) => {
                if (response.result !== 200) {
                  checkDataResult = false
                  this.$baseMessage(
                     '您已存在离职申请，无法提交',
                    'error',
                    'vab-hey-message-success'
                  )
                }
              })
            }
            if(checkDataResult){
              saveData(this.formData)
                .then((response) => {
                  this.$baseMessage(
                    this.formData.isAdd ? '新增成功！' : '修改成功!',
                    'success',
                    'vab-hey-message-success'
                  )
                  this.close()
                  this.$emit('fetch-data')
                })
                .catch((err) => {
                  this.$baseMessage(
                    this.formData.isAdd ? '新增失败！' : '修改失败!',
                    'error',
                    'vab-hey-message-error'
                  )
                })
            }
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
