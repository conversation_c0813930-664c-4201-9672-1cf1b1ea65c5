<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="姓名" prop="userName">
            <el-input
              v-model="queryForm.userName"
              clearable
              placeholder="请输入姓名"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="所属项目" prop="projectName">
            <el-input
              v-model="queryForm.projectName"
              clearable
              placeholder="请输入所属项目"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="queryForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleDateChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '申请日期'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <el-image
            v-else-if="(item.prop === 'startImagePath' || item.prop === 'endImagePath') && row[item.prop]"
            :src="row[item.prop]"
            :preview-src-list="[row[item.prop]]"
            fit="contain"
            style="width: 80px; height: 60px"
          />
          <span v-else>
            {{ row[item.prop] || '' }}
          </span>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>
<script>
  import tableMix from '@/views/mixins/table'
  import {
    getDataListByPage
  } from '@/api/administrativeManagement/externalWorkersAttendance-api'

  export default {
    name: 'TemporaryEmployment',
    components: {
    },
    mixins: [tableMix],
    data() {
      return {
        formMaxHeight: 2,
        columns: [
          {
            label: '姓名',
            prop: 'userName',
            width: '80px',
          },
          {
            label: '所属项目',
            prop: 'projectName',
          },
          {
            label: '用工类型',
            prop: 'employmentTypeName',
          },
          {
            label: '所属日期',
            prop: 'belongDate',
            width: '100px',
          },
          {
            label: '用工数量',
            prop: 'laborQuantity',
          },
          {
            label: '用工时长(小时)',
            prop: 'workHours',
            width: '80px',
          },
          {
            label: '开始时间',
            prop: 'startTime',
            width: '160px',
          },
          {
            label: '地点',
            prop: 'startAddress',
          },
          {
            label: '照片',
            prop: 'startImagePath',

          },
          {
            label: '打卡说明',
            prop: 'startDescribe',
          },
          {
            label: '结束时间',
            prop: 'endTime',
            width: '160px'
          },
          {
            label: '地点',
            prop: 'endAddress',
          },{
            label: '照片',
            prop: 'endImagePath',
          },{
            label: '说明',
            prop: 'endDescribe',
          },
        ],
        queryForm: {
          userName: '',
          projectName: '',
          dateRange: [],
          startDate: '',
          endDate: ''
        },
      }
    },
    created() {
      // this.getDictListByCodes()
      this.fetchData()
    },
    methods: {
      handleDateChange(dateRange) {
        if (dateRange && dateRange.length === 2) {
          this.queryForm.startDate = dateRange[0];
          this.queryForm.endDate = dateRange[1];
        } else {
          this.queryForm.startDate = '';
          this.queryForm.endDate = '';
        }
      },
      resetFormPage() {
        this.resetForm('form');
        this.queryForm.dateRange = [];
        this.queryForm.startDate = '';
        this.queryForm.endDate = '';
      },
      fetchData() {
        this.listLoading = true

        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
          startDate: this.queryForm.startDate,
          endDate: this.queryForm.endDate
        }
        getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
