<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="所属项目" prop="projectName">
            <el-input
              v-model="queryForm.projectName"
              clearable
              placeholder="请输入项目名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="申请人" prop="createByName">
            <el-input
              v-model="queryForm.createByName"
              clearable
              placeholder="请输入申请人姓名"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel>
        <el-button
          v-permissions="{ permission: ['projectTravel:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>

        <el-button
          :disabled="exportLoading"
          icon="el-icon-download"
          type="warning"
          @click.native="exportBtn('临时租赁设备记录.xlsx')"
        >
          导出
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      row-key="id"
      :size="lineHeight"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '开始时间'">
            {{ dateFormat(row.startTime) }}
          </span>
          <span v-else-if="item.label === '结束时间'">
            {{ dateFormat(row.endTime) }}
          </span>
          <span v-else-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['projectTravel:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 0 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item @click.native.prevent="showReport(row)">
                <i class="el-icon-video-play" />
                查看报表
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['projectTravel:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit ref="edit" :tree-data="treeData" @fetch-data="fetchData" />
    <travelDetail ref="tableDetail" />
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />
    <VabTableExport ref="tableExport" @refreshPage="resetFormPage" />
  </div>
</template>

<script>
  import {
    deleteData,
    getDataListByPage,
    exportData,
  } from '@/api/administrativeManagement/deviceLeasing-api'
  import { getProjectList } from '@/api/project'
  import TableEdit from './components/deviceLeasingEdit.vue'
  import travelDetail from './components/deviceLeasingDetail.vue'
  import tableMix from '@/views/mixins/table'
  import { reportAddr } from '@/utils/constants'
  import { parseTime } from '@/utils'
  //搜索条件 所属部门必备api
  import { getDepartList } from '@/api/system/depart-api'
  import { formatEleDropDownTree } from '@/utils/util.js'
  //ending
  export default {
    name: 'ProjectTravel',
    components: {
      TableEdit,
      travelDetail,
    },
    mixins: [tableMix],
    data() {
      return {
        columns: [
          { label: '单号', prop: 'serialNumber', width: 220 },
          { label: '所属项目', prop: 'projectName', width: 220 },
          { label: '开始时间', prop: 'startTime', width: 220 },
          { label: '结束时间', prop: 'endTime', width: 220 },
          { label: '时长', prop: 'duration', width: 100 },
          { label: '租赁数量', prop: 'leasingCount', width: 120 },
          { label: '租赁单价', prop: 'leasingPrice', width: 120 },
          { label: '申请事由', prop: 'leasingReason', width: 200 },
          { label: '租赁金额', prop: 'leasingAmount', width: 120 },
          { label: '事由', prop: 'leasingReason', width: 200 },
          { label: '申请人', prop: 'createByName', width: 100 },
          { label: '申请日期', prop: 'applicationDate', width: 120 },
          { label: '审批状态', prop: 'approvalResultName', width: 100 },
        ],

        treeData: [],
        queryForm: {
          createByName: '',
          projectName: '',
        },
        departData: [],
        projectDataList: [],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
      }
    },
    computed: {},
    created() {
      this.fetchData()
      this.getProjectList()
      this.loadDepartData() //搜索条件所属项目拿数据的方法
    },
    methods: {
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportData({ ...this.queryForm, ...columns }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },

      showReport(row) {
        const params = '&id=' + row.id
        let url = reportAddr
        if (row.tavelModeName === '私车公用') {
          url =
            reportAddr +
            '/ReportServer?reportlet=/oaNew/travel/travelProject.cpt' +
            params
        } else {
          url =
            reportAddr +
            '/ReportServer?reportlet=/oaNew/travel/travelProjectExct.cpt' +
            params
        }

        window.open(url)
      },
      getProjectList() {
        const params = {
          approvalResult: '2',
        }
        getProjectList(params).then((response) => {
          this.projectDataList = response.result
        })
      },
      dateFormat(date) {
        if (!date) {
          return ''
        }
        return parseTime(new Date(date), '{y}-{m}-{d} {h}:{i}')
      },
      async fetchData() {
        //async 异步数据处理方法
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.queryForm.type = 'project'
        this.listLoading = true
        const { result } = await getDataListByPage(this.queryForm)
        this.list = result.records //把请求到的数据赋值给表格
        this.pageInfo.total = Number(result.total)
        this.listLoading = false
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteData({ id: row.id }).then(() => {
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
          })
        }
      },
      handleAdd() {
        this.$refs['edit'].showEdit() //看到这个之后，要去找showEdit 方法
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit(row)
      },
      showAdd(type) {
        this.$refs['edit'].showEdit(type)
      },
      handleDetail(row) {
        this.$refs['tableDetail'].showDetail(row)
      },
      // 搜索条件必备下面的方法
      resetFormPage() {
        // this.departId = ''
        this.queryForm.departName = ''
        this.queryForm.departId = ''
        this.resetForm('form')
      },
      async loadDepartData() {
        await getDepartList({}).then((response) => {
          const json = JSON.parse(
            JSON.stringify(response.result).replace(/departName/g, 'name')
          )
          this.departData = formatEleDropDownTree(json, '-1')
        })
      },
      getIndex(index) {
        return (this.pageInfo.curPage - 1) * this.pageInfo.pageSize + index + 1
      },
    },
  }
</script>

<style lang="scss" scoped></style>
