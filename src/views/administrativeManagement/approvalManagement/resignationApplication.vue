<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="申请部门" prop="createDepartName">
            <el-input
              v-model="queryForm.createDepartName"
              clearable
              placeholder="请输入申请部门"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="申请人" prop="createByName">
            <el-input
              v-model="queryForm.createByName"
              placeholder="请输入申请人名称"
            />
          </el-form-item>
          <el-form-item label="审批状态" prop="approvalResult">
            <el-select
              ref="selectFlow"
              v-model="queryForm.approvalResult"
              filterable
              placeholder="请选择审批状态"
              style="width: 200px"
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in approvalResultNameList"
                :key="item.id"
                :label="item.approvalResultName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          v-permissions="{ permission: ['resignationApplication:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
        <el-button
          :disabled="exportLoading"
          icon="el-icon-download"
          type="warning"
          @click.native="exportBtn('离职记录.xlsx')"
        >
          导出
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column
        align="center"
        :selectable="isDeleteFlag"
        type="selection"
        width="55"
      />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else-if="item.label === '申请日期'">
            {{ row.applyTime | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '入职日期'">
            {{ row.onBoardTime | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '离职日期'">
            {{ row.terminationTime | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['resignationApplication:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['resignationApplication:del'] }"
                divided
                @click.native.prevent="handleDelete(row)"
              >
                <span style="color: #fd5353">
                  <i class="el-icon-delete-solid" />
                  删除
                </span>
              </el-dropdown-item>
              <el-dropdown-item divided @click.native.prevent="showReport(row)">
                <i class="el-icon-s-order" />
                查看报表
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <!-- 编辑-->
    <table-edit ref="tableEdit" @fetch-data="fetchData" />
    <!-- 详情-->
    <table-detail ref="tableDetail" />
    <!-- 部门选择 -->
    <!-- <VabWorkFlowDepart ref="VabWorkFlowDepart"  /> -->
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />
    <VabTableExport ref="tableExport" @refreshPage="resetFormPage" />
  </div>
</template>

<script>
  import {
    getDataListByPage,
    deleteData,
    exportData,
  } from '@/api/administrativeManagement/resignationApplication-api.js'
  import tableEdit from './components/resignationApplicationEdit'
  import tableMix from '@/views/mixins/table'
  import tableDetail from './components/resignationApplicationDetail'
  import { reportAddr } from '@/utils/constants'
  export default {
    name: 'ResignationApplication',
    components: {
      tableEdit,
      tableDetail,
    },
    mixins: [tableMix],
    data() {
      return {
        columns: [
          {
            label: '单号',
            prop: 'serialNumber',
            width: '250',
          },
          {
            label: '申请人',
            prop: 'createByName',
          },
          {
            label: '申请部门',
            prop: 'createDepartName',
          },
          {
            label: '申请日期',
            prop: 'applyTime',
            width: '120',
          },
          {
            label: '离职原因',
            prop: 'reasonForTermination',
            width: '120',
          },
          {
            label: '入职日期',
            prop: 'onBoardTime',
            width: '120',
          },
          {
            label: '离职日期',
            prop: 'terminationTime',
            width: '120',
          },
          {
            label: '离职说明',
            prop: 'resignationDescription',
          },
          {
            label: '审批状态',
            prop: 'approvalResultName',
            width: '100',
          },
        ],
        queryForm: {
          createDepartName: '',
          createByName: '',
        },
      }
    },
    computed: {},
    created() {
      this.fetchData()
    },
    methods: {
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportData({ ...this.queryForm, ...columns }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },
      showReport(data) {
        const params = '&id=' + data.id
        const url =
          reportAddr +
          '/ReportServer?reportlet=/oaNew/resignationApplication/resignationApplication.cpt' +
          params
        window.open(url)
      },
      //重置
      resetFormPage() {
        this.resetForm('form')
      },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      handleAdd() {
        this.$refs['tableEdit'].showEdit()
      },
      handleEdit(row) {
        this.$refs['tableEdit'].showEdit(row)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前离职申请吗', null, async () => {
            deleteData({ id: row.id })
              .then(() => {
                this.pageInfo.curPage = 1
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      deleteList() {
        if (this.selectRows.length === 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
        } else {
          const ids = this.selectRows.map((item) => item.id).join(',')
          this.$baseConfirm(
            '你确定要批量删除离职申请数据？',
            null,
            async () => {
              await deleteData({ id: ids })
                .then(() => {
                  this.pageInfo.curPage = 1
                  this.fetchData()
                  this.$baseMessage(
                    '批量删除成功！',
                    'success',
                    'vab-hey-message-success'
                  )
                })
                .catch((err) => {
                  this.$baseMessage(
                    '批量删除失败！',
                    'error',
                    'vab-hey-message-error'
                  )
                })
            }
          )
        }
      },
      async fetchData() {
        this.listLoading = true

        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        delete queryForm.departName
        const {
          result: { records, total },
        } = await getDataListByPage(queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
    },
  }
</script>
<style lang="scss" scoped></style>
