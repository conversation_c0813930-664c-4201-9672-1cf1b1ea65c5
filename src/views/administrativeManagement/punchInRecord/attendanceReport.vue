<template>
  <div
  ref="custom-table"
  class="custom-table-container"
  :class="{ 'vab-fullscreen': isFullscreen }"
  >
  <vab-query-form>
    <vab-query-form-top-panel>
      <el-form
        ref="form"
        :inline="true"
        label-width="80px"
        :model="queryForm"
        @submit.native.prevent
      >
        <el-form-item label="用户姓名" prop="userName" label-width="80px">
          <el-input v-model="queryForm.userName"/>
        </el-form-item>
        <el-form-item label="所属部门" prop="departId">
          <t-form-tree
            v-model="queryForm.departId"
            :default-props="{ children: 'children', label: 'departName' }"
            :show-top="false"
            :tree-data="allDepartList"
          />
        </el-form-item>
        <el-form-item label="搜索时间范围" prop="queryStartData" label-width="120px">
          <el-date-picker :clearable="false" :picker-options="pickerOptions" v-model="queryForm.queryStartData" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择最早时间" />
        </el-form-item> -
        <el-form-item prop="queryEndData">
          <el-date-picker :clearable="false" :picker-options="pickerOptions" v-model="queryForm.queryEndData" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择最晚时间" />
        </el-form-item>
        <el-form-item>
          <el-button
            icon="el-icon-search"
            native-type="submit"
            type="primary"
            @click="handleQuery"
          >
            查询
          </el-button>
          <el-button icon="el-icon-refresh-right" @click.native="resetForm('form')">重置</el-button>
        </el-form-item>
      </el-form>
    </vab-query-form-top-panel>
    <vab-query-form-left-panel :span="12">
      <el-button
          :disabled="exportLoading"
          icon="el-icon-download"
          type="warning"
          @click="exportBtn('考勤报表.xlsx')"
        >
          导出
        </el-button>
    </vab-query-form-left-panel>
    <vab-query-form-right-panel :span="12">
      <el-button
        style="margin: 0 10px 10px 0 !important"
        type="primary"
        @click="clickFullScreen"
      >
        <vab-icon
          :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
        />
        表格全屏
      </el-button>
      <el-popover
        ref="popover"
        popper-class="custom-table-checkbox"
        trigger="hover"
      >
        <el-radio-group v-model="lineHeight">
          <el-radio label="medium">大</el-radio>
          <el-radio label="small">中</el-radio>
          <el-radio label="mini">小</el-radio>
        </el-radio-group>
        <template #reference>
          <el-button style="margin: 0 10px 10px 0 !important" type="primary">
            <vab-icon icon="line-height" />
            表格尺寸
          </el-button>
        </template>
      </el-popover>
      <el-popover popper-class="custom-table-checkbox" trigger="hover">
        <el-checkbox-group v-model="checkList">
          <vab-draggable v-bind="dragOptions" :list="columns">
            <div v-for="(item, index) in columns" :key="item + index">
              <vab-icon icon="drag-drop-line" />
              <el-checkbox
                :disabled="item.disableCheck === true"
                :label="item.label"
              >
                {{ item.label }}
              </el-checkbox>
            </div>
          </vab-draggable>
        </el-checkbox-group>
        <template #reference>
          <el-button
            icon="el-icon-setting"
            style="margin: 0 0 10px 0 !important"
            type="primary"
          >
            可拖拽列设置
          </el-button>
        </template>
      </el-popover>
    </vab-query-form-right-panel>
  </vab-query-form>
     <el-table
       ref="tableSort"
       v-loading="listLoading"
       border
       :data="list"
       :height="height"
       :size="lineHeight"
       stripe
       @selection-change="setSelectRows"
     >
       <el-table-column
         v-for="(item, index) in finallyColumns"
         :key="index"
         :align="item.center ? item.center : 'center'"
         :label="item.label"
         :prop="item.prop"
         :sortable="item.sortable ? item.sortable : false"
         :width="item.width ? item.width : 'auto'"
       >
      <template #default="{ row }">
         <span v-if="item.label === '上班打卡结果' || item.label === '下班打卡结果'">
            <el-tag type="success" v-if="row[item.prop] === '外勤'">{{ row[item.prop]}}</el-tag>
            <el-tag type="danger" v-else-if="row[item.prop] === '迟到'">{{ row[item.prop] }}</el-tag>
            <el-tag type="warning" v-else-if="row[item.prop] === '缺卡'">{{ row[item.prop] }}</el-tag>
            <el-tag v-else-if="row[item.prop] === '正常'">{{ row[item.prop] }}</el-tag>
            <span v-else>{{ row[item.prop] }}</span>
         </span>
         <!-- <span v-else-if="item.label === '下班打卡结果'">
            <el-tag type="success" v-if="row[8] === '外勤'">{{ row[8] }}</el-tag>
            <el-tag type="danger" v-else-if="row[8] === '迟到'">{{ row[8] }}</el-tag>
            <el-tag type="warning" v-else-if="row[8] === '缺卡'">{{ row[8] }}</el-tag>
            <el-tag v-else-if="row[8] === '正常'">{{ row[8] }}</el-tag>
            <span v-else>{{ row[8] }}</span>
         </span> -->
         <span v-else>{{ row[item.prop] }}</span>
       </template>
       </el-table-column>
       <template #empty>
         <el-image
           class="vab-data-empty"
           :src="require('@/assets/empty_images/data_empty.png')"
         />
       </template>
     </el-table>
  </div>
</template>

<script>
import { getImportPunchInPersonRecordByPage, exportData } from '@/api/oa/oaAttendance-api'
import tableMix from '@/views/mixins/table'
import { parseTime } from '@/utils/th_utils'

export default {
  name: 'attendanceReport',
  mixins: [tableMix],
  data() {
    return {
      columns: [
        {
          label: '用户姓名',
          prop: '0',
        },
        {
          label: '所属部门',
          prop: '1',
        },
        {
          label: '考勤组',
          prop: '2',
        },
        {
          label: '日期',
          prop: '3',
        },
        {
          label: '班次',
          prop: '4',
          width: '265'
        },
        {
          label: '上班打卡时间',
          prop: '5',
        },
        {
          label: '上班打卡结果',
          prop: '6',
        },
        {
          label: '下班打卡时间',
          prop: '7',
        },
        {
          label: '下班打卡结果',
          prop: '8',
        },
        {
          label: '请假类型及时间',
          prop: '9',
        },
      ],
      queryForm:{
        bizKey:'',
        excelName:'',
        userName: '',
        departId:'',
        queryStartData: '',
        queryEndData: ''
      },
      pickerOptions:{
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    }
  },
  watch: {

  },
  created() {
    this.queryForm.queryStartData = parseTime(this.getFirstDayOfMonth(),'{y}-{m}-{d}')
    this.queryForm.queryEndData = parseTime(new Date(),'{y}-{m}-{d}')
    this.fetchData()
  },
  mounted() {

  },
  beforeDestroy(){

  },
  methods: {
    getFirstDayOfMonth() {
      const now = new Date()
      return new Date(now.getFullYear(), now.getMonth(), 1)
    },
    exportBtn(excelName) {
      this.exportLoading = true
      this.queryForm.bizKey = this.$route.name
      this.queryForm.excelName = excelName
      exportData(this.queryForm).then((response) => {
        const link = document.createElement('a')
        link.download = excelName
        link.href = window.URL.createObjectURL(new Blob([response]))
        document.body.appendChild(link)
        link.click()
        link.download = ''
        document.body.removeChild(link)
        URL.revokeObjectURL(response)
      })
      this.exportLoading = false
    },
    fetchData() {
      this.listLoading = true
      getImportPunchInPersonRecordByPage(this.queryForm).then(response => {
        const result = response.result
        this.list = result
        this.listLoading = false
      })
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
