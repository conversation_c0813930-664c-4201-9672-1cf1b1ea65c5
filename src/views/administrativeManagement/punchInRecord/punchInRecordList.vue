<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="用户" label-width="40px" prop="realName">
            <el-input v-model="queryForm.realName" />
          </el-form-item>
          <el-form-item
            label="搜索时间范围"
            label-width="120px"
            prop="belongStartTime"
          >
            <el-date-picker
              v-model="queryForm.belongStartTime"
              format="yyyy-MM-dd"
              placeholder="选择最早时间"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          -
          <el-form-item prop="belongEndTime">
            <el-date-picker
              v-model="queryForm.belongEndTime"
              format="yyyy-MM-dd"
              placeholder="选择最晚时间"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12" />
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '打卡类型'">
            <el-tag v-if="row.type === 1 && row.category === 1" type="success">
              上班打卡
            </el-tag>
            <el-tag
              v-else-if="row.type === 1 && row.category === 2"
              type="success"
            >
              下班打卡
            </el-tag>
            <el-tag v-else-if="row.type === 0 && row.category === 1">
              外勤（上午）打卡
            </el-tag>
            <el-tag v-else>外勤（下午）打卡</el-tag>
          </span>
          <span v-else-if="item.label === '外勤打卡照片'">
            <el-image
              v-if="row.punchImg"
              fit="cover"
              :preview-src-list="[row.punchImg]"
              :src="row.punchImg"
              style="width: 60px; height: 40px"
              title="点击预览"
            />
          </span>
          <span v-else-if="item.label === '地点'">
            {{ row.place }}
            <el-link
              v-if="row.place !== ''"
              type="primary"
              @click="showBMap(row)"
            >
              <i class="el-icon-map-location"></i>
            </el-link>
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <BMapShow ref="BMapShow" />
  </div>
</template>

<script>
  import { getPunchInPersonRecordByPage } from '@/api/oa/oaAttendance-api'
  import tableMix from '@/views/mixins/table'
  import { qqMapToBMap } from '@/utils/util.js'
  import BMapShow from '@/views/common/BMap/BMapShow.vue'

  export default {
    name: 'OaRecord',
    components: {
      BMapShow,
    },
    mixins: [tableMix],
    data() {
      return {
        checkList: [
          '用户姓名',
          '公司/项目',
          '打卡类型',
          '所属日期',
          '打卡时间',
          '地点',
          '外勤打卡照片',
          '说明',
        ],
        columns: [
          {
            label: '用户姓名',
            prop: 'realName',
          },
          {
            label: '公司/项目',
            prop: 'bizName',
            width: '200',
          },
          {
            label: '打卡类型',
            prop: 'type',
            width: '150',
          },
          {
            label: '所属日期',
            prop: 'belongTime',
            width: '120',
          },
          {
            label: '打卡时间',
            prop: 'clockTime',
            width: '180',
          },
          {
            label: '地点',
            prop: 'place',
            width: '640',
          },
          {
            label: '外勤打卡照片',
            prop: 'punchImg',
            width: '130',
          },
          {
            label: '说明',
            prop: 'remark',
          },
        ],
        queryForm: {
          bizId: '', //项目或标段
          userName: '',
          realName: '',
          type: '', //打卡类型 1 上下班打卡 0 外勤打卡
          userId: '', //用户id
          belongStartTime: '', //打卡日期--开始查询日期
          belongEndTime: '', //打卡日期--结束打卡日期
        },
      }
    },
    watch: {},
    created() {
      this.fetchData()
    },
    mounted() {},
    beforeDestroy() {},
    methods: {
      showBMap(row) {
        var point = qqMapToBMap(row.longitude, row.latitude)
        let marker = {
          position: {
            lng: point[0],
            lat: point[1],
          },
          info:
            row.realName +
            '<br/>' +
            '打卡时间：' +
            row.clockTime +
            '<br/>' +
            '打卡地址：' +
            row.place,
        }
        this.$refs.BMapShow.showBaiDuMap(marker)
      },
      fetchData() {
        const curPage = this.pageInfo.curPage
        const pageSize = this.pageInfo.pageSize
        this.listLoading = true
        getPunchInPersonRecordByPage(curPage, pageSize, this.queryForm).then(
          (response) => {
            const result = response.result
            this.list = result.list
            this.pageInfo.total = Number(result.total)
            this.listLoading = false
          }
        )
      },
    },
  }
</script>

<style lang="scss" scoped></style>
