<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="100px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="所属部门" prop="departId">
            <t-form-tree
              v-model="queryForm.departId"
              :default-props="{ children: 'children', label: 'departName' }"
              :show-top="false"
              :tree-data="departTreeData"
            />
          </el-form-item>
          <!-- <el-form-item label="使用部门" label-width="110px" prop=" departName">
            <el-input
              v-model="queryForm.departName"
              clearable
              placeholder="输入部门名称"
            />
          </el-form-item> -->

          <el-form-item label="车牌号" label-width="110px" prop=" departName">
            <el-input
              v-model="queryForm.plateNumber"
              clearable
              placeholder="输入车牌号"
            />
          </el-form-item>

          <el-form-item label="申请人" label-width="110px" prop=" departName">
            <el-input
              v-model="queryForm.userName"
              clearable
              placeholder="输入申请人名称"
            />
          </el-form-item>

          <el-form-item label="申请时间" label-width="110px" prop=" departName">
            <el-date-picker
              v-model="queryForm.applicationTime"
              end-placeholder="结束日期"
              range-separator="至"
              start-placeholder="开始日期"
              type="datetimerange"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click.native="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          :disabled="exportLoading"
          icon="el-icon-download"
          type="warning"
          @click.native="exportBtn('私车公用统计.xlsx')"
        >
          导出
        </el-button>
        <!-- <el-button
          v-permissions="{ permission: ['leave:del'] }"
          icon="el-icon-delete"
          type="danger"
          @click="delBatch"
        >
          批删
        </el-button> -->
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.prop === 'outputVolume'">
            {{ getOutputVolumeName(row) }}
          </span>
          <span
            v-else-if="
              item.prop === 'startTime' ||
              item.prop === 'endTime' ||
              item === 'createTime'
            "
          >
            {{ dateFormat(row[item.prop]) }}
          </span>
          <span v-else-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" fixed="right" label="操作" width="320">
        <template slot-scope="{ row }">
          <el-button
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>

              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item @click.native.prevent="showReport(row)">
                <i class="el-icon-video-play" />
                查看报表
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column> -->
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <!-- 新增编辑 -->
    <!-- <table-edit
      ref="edit"
      :output-volume-list="outputVolumeList"
      @fetch-data="fetchData"
    /> -->
    <!-- 详情 -->
    <!-- <leave-detail ref="tableDetail" /> -->
    <!-- <tableDetail ref="tableDetail" :output-volume-list="outputVolumeList" /> -->
    <!-- 部门选择
    <VabWorkFlowDepart ref="VabWorkFlowDepart" /> -->
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />
    <VabTableExport ref="tableExport" @refreshPage="resetFormPage" />
  </div>
</template>
<script>
  import {
    getPrivateDataListByPage,
    exportData,
  } from '@/api/administrativeManagement/vehicleStatistics-api'
  import tableMix from '@/views/mixins/table'
  // import tableDetail from './components/vehicleAllocationDetail.vue'
  import { getDictList } from '@/api/system/dict-api'
  import { getDepartList } from '@/api/system/depart-api'
  import { reportAddr } from '@/utils/constants'
  import { formatEleDropDownTree } from '@/utils/util'
  import { mapGetters } from 'vuex'
  import { dateFormat } from '../../../utils/th_utils'

  export default {
    name: 'VehicleStatistics',
    components: {
      // tableDetail,
    },
    mixins: [tableMix],
    data() {
      return {
        outputVolumeList: [
          { label: '1.8及以上', value: '1', subsidyStandard: '1.6元/公里' },
          { label: '1.8以下', value: '2', subsidyStandard: '1.1元/公里' },
        ],
        departTreeData: [],
        hasCard: true,
        columns: [
          {
            label: '单号',
            prop: 'serialNumber',
            width: '230',
          },
          {
            label: '申请时间',
            prop: 'createTime',
            width: '180',
          },
          {
            label: '车牌号',
            prop: 'plateNumber',
            width: '150',
          },
          {
            label: '车辆排量',
            prop: 'outPutVloumeName',
            width: '100',
          },
          {
            label: '车辆使用部门',
            prop: 'departName',
            width: '180',
          },
          {
            label: '申请人',
            prop: 'userName',
          },
          {
            label: '事由',
            prop: 'remark',
            width: '240',
          },
          {
            label: '开始使用时间',
            prop: 'startTime',
            width: '150',
          },
          {
            label: '出发公里数',
            prop: 'startKm',
            width: '150',
          },
          {
            label: '结束使用时间',
            prop: 'endTime',
            width: '150',
          },
          {
            label: '回车公里数',
            prop: 'afterKm',
            width: '150',
          },
          {
            label: '行程公里数',
            prop: 'totalKm',
            width: '150',
          },
          {
            label: '补贴标准',
            prop: 'subsidyStandard',
            width: '150',
          },

          {
            label: '补贴金额',
            prop: 'money',
            width: '100',
          },
        ],
        queryForm: {
          departName: '',
          applicationTime: '',
          departId: '',
          plateNumber: '',
          userName: '',
        },
        pageInfo: {
          curPage: 1,
          pageSize: 10,
        },
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        selectedLeaveTypeQuery: [],
        selectLeaveType: [],
        departData: [],
      }
    },
    computed: {
      ...mapGetters({
        departList: 'acl/departList',
      }),
    },
    created() {
      this.loadDepartData()
      this.fetchData()
      this.getDepartList()
    },
    mounted() {},

    methods: {
      dateFormat,
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportData({
          departName: this.queryForm.departName,
          startTime: this.queryForm.applicationTime[0],
          endTime: this.queryForm.applicationTime[1],
          userName: this.queryForm.userName,
          plateNumber: this.queryForm.plateNumber,
          departId: this.queryForm.departId,
          ...columns,
        }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },

      getOutputVolumeName(row) {
        if (!row.outputVolume) return ''
        let outputVolumeName = this.outputVolumeList.find(
          (item) => item.value === row.outputVolume
        ).label
        return outputVolumeName
      },
      // showReport(row) {
      //   const params = '&id=' + row.id
      //   let url =
      //     reportAddr + '/ReportServer?reportlet=/oaNew/leave/leave.cpt' + params
      //   window.open(url)
      // },
      formatText(text) {
        if (!text) return ''
        // Truncate text if it exceeds 200 characters
        const maxLength = 200
        let truncatedText = text
        if (text.length > maxLength) {
          truncatedText = text.substring(0, maxLength) + '...'
        }
        return truncatedText
          .replace(/ /g, '&nbsp;') // 保留空格
          .replace(/\n/g, '<br>') // 保留换行
      },
      getDepartList() {
        getDepartList({}).then((response) => {
          this.departTreeData = response.result
        })
      },
      filterNode(value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      handleNodeClick(data, checked, node) {
        if (this.departId) {
          if (checked) {
            this.$refs.departTree.setCheckedKeys([])
            this.$refs.departTree.setCheckedKeys([data.id])
          } else {
            this.departId = ''
            this.$refs.departTree.setCheckedKeys([])
          }
        } else {
          this.departId = data.id
          this.$refs.departTree.setCheckedKeys([data.id])
        }
      },
      async loadDepartData() {
        await getDepartList({}).then((response) => {
          const json = JSON.parse(
            JSON.stringify(response.result).replace(/departName/g, 'name')
          )
          this.departData = formatEleDropDownTree(json, '-1')
        })
      },

      //重置
      resetFormPage() {
        // this.$refs.departTree.setCheckedKeys([])
        this.queryForm.departName = ''
        this.queryForm.applicationTime = ''
        this.resetForm('form')
      },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      handleAdd() {
        this.$refs['edit'].showEdit('', this.project)
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit(row, '')
      },
      handleDelete(row) {
        // if (row.id) {
        //   this.$baseConfirm(
        //     '你确定要删除当前请假申请信息吗',
        //     null,
        //     async () => {
        //       deleteData({ id: row.id })
        //         .then((response) => {
        //           this.pageInfo.curPage = 1
        //           this.fetchData()
        //           this.$baseMessage(
        //             '删除成功!',
        //             'success',
        //             'vab-hey-message-success'
        //           )
        //         })
        //         .catch((err) => {
        //           this.$baseMessage(
        //             '删除失败!',
        //             'error',
        //             'vab-hey-message-error'
        //           )
        //         })
        //     }
        //   )
        // }
      },
      delBatch() {
        // if (this.selectRows.length == 0) {
        //   this.$baseMessage(
        //     '请最少选中一条记录!',
        //     'error',
        //     'vab-hey-message-error'
        //   )
        // } else {
        //   const ids = this.selectRows.map((item) => item.id).join(',')
        //   this.$baseConfirm(
        //     '你确定要批量删除请假申请数据吗',
        //     null,
        //     async () => {
        //       deleteData({ id: ids })
        //         .then((response) => {
        //           this.pageInfo.curPage = 1
        //           this.fetchData()
        //           this.$baseMessage(
        //             '批量删除成功！',
        //             'success',
        //             'vab-hey-message-success'
        //           )
        //         })
        //         .catch((err) => {
        //           this.$baseMessage(
        //             '批量删除失败！',
        //             'error',
        //             'vab-hey-message-error'
        //           )
        //         })
        //     }
        //   )
        // }
      },
      async fetchData() {
        this.listLoading = true
        const queryForm = {
          departName: this.queryForm.departName,
          startTime: this.queryForm.applicationTime[0],
          endTime: this.queryForm.applicationTime[1],
          userName: this.queryForm.userName,
          plateNumber: this.queryForm.plateNumber,
          departId: this.queryForm.departId,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        const {
          result: { records, total },
        } = await getPrivateDataListByPage(queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
      getIndex(index) {
        return (this.pageInfo.curPage - 1) * this.pageInfo.pageSize + index + 1
      },
    },
  }
</script>
<style lang="scss" scoped></style>
