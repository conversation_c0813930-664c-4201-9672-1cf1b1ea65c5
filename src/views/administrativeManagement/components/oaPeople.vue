<template>
  <el-dialog
    :title="name + title"
    :visible.sync="isShowDialog"
    v-if="isShowDialog"
    width="800px"
    v-drag
    center
    :close-on-click-modal="false"
    top="5vh"
  >
    <div class="my-transfer">
      <el-transfer
        v-loading="loading"
        :titles="['可选考勤人员','已选考勤人员']"
        filterable
        :filter-method="filterMethod"
        :props="{
          key: 'id',
          label: 'userName'
        }"
        filter-placeholder="请输入姓名"
        @change="handleChange"
        v-model="value"
        :data="userList">
      </el-transfer>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="isShowDialog = false">
        关闭
      </el-button>
      <el-button type="primary" @click="save">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getPunchInPerson, addPunchIn<PERSON>erson, deletePunchIn<PERSON>erson } from '@/api/oa/oaAttendance-api'
import { getUserList } from '@/api/system/user-api'
import { mapGetters } from 'vuex'

export default {
  name: 'oaPeople',
  components: {},
  props: {
    bizId: {
      type: String,
      default: () => {
        return ''
      }
    },
    name: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      title: '考勤人员',
      isShowDialog: false,
      userList: [],
      value: [],
      intValue: [],
      loading: false,
      filterMethod(query, item) {
        return item.userName.indexOf(query) > -1;
      }
    }
  },
  computed: {
    ...mapGetters({
      userId: 'user/userId'
    }),
  },
  watch:{

  },
  created() {
    this.getUserList()
  },
  methods: {
    getPunchInPerson(){
      this.loading = true
      var data = {
        bizId: this.bizId, //项目或标段
      }
      getPunchInPerson(data).then(response=>{
        var result = response.result
        this.value = result.map(item=>item.userId)
        this.intValue = result.map(item=>item.userId)
        this.intIds = result
        this.loading = false
      })
    },
    async getUserList() {
      const response = await getUserList({})
      this.userList = response.result
    },
    handleChange(value, direction, movedKeys) {
      // console.log(value, direction, movedKeys)
    },
    showDateDialog() {
      this.isShowDialog = true
      this.getPunchInPerson()
    },
    save(){
      var addUser = []
      var delUser = []
      this.value.forEach(item=>{ //遍历没有的加上
        if(this.intValue.indexOf(item) < 0){
          addUser.push(item)
        }
      })
      this.intValue.forEach(item=>{//遍历删除的添加删除
        if(this.value.indexOf(item) < 0){
          delUser.push(item)
        }
      })
      if(addUser.length > 0){
          var data = {
          bizId: this.bizId,
          userId: this.userId,
          personInfo:[]
        }
        addUser.forEach(item => {
          var obj = {
            id: item,
            name: this.userList.find(val=>item===val.id).userName
          }
          data.personInfo.push(obj)
        })
        addPunchInPerson(data).then(response=>{
          const result = response.result.result
          if (result) {
            this.$notify({
              title: '信息',
              message: '保存成功！',
              type: 'success',
              duration: 2000
            })
            this.isShowDialog = false
          } else {
            this.$notify({
              title: '信息',
              message: response.result.info,
              type: 'error',
              duration: 2000
            })
          }
        })
      }
      if(delUser.length > 0){
        var ids = []
        delUser.forEach(item=>{
          ids.push(this.intIds.find(val=>item===val.userId).id)
        })
        var delData = {
          ids: ids.join(',')
        }
        deletePunchInPerson(delData).then(response=>{
          const result = response.result
          if (result) {
            this.$notify({
              title: '信息',
              message: '保存成功！',
              type: 'success',
              duration: 2000
            })
            this.isShowDialog = false
          } else {
            this.$notify({
              title: '信息',
              message: response.result.info,
              type: 'error',
              duration: 2000
            })
          }
        })
      }
    },
  }
}
</script>
<style scoped lang="scss">
.my-transfer ::v-deep .el-transfer-panel__body{
  height: 500px;
  .el-transfer-panel__list.is-filterable{
    height: 448px;
  }
}
.my-transfer ::v-deep .el-transfer-panel{
  width: 250px;
}
</style>

