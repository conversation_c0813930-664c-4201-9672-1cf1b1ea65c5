<template>
  <el-dialog
    :title="name + title"
    :visible.sync="isShowDialog"
    width="1000px"
    center
    :close-on-click-modal="false"
    v-drag
    top="5vh"
  >
    <!--日历-->
    <el-calendar v-model="curDate">
      <template slot="dateCell" slot-scope="{data}">
        <div style="width: 100%;height: 100%;padding: 10px;" @click="changeDate(data.day)">
          <p style="margin: 0px;font-size: 15px;">
            {{ data.day.split('-').slice(1).join('-') }}<br/>
           </p>
          <!--标记-->
          <div v-for="(i, index) in dayTime" :key="index">
            <div v-if="data.day==i" class="budge"></div>
          </div>
        </div>
      </template>
    </el-calendar>
    <div>
      上班时间：
      <el-time-picker
        value-format="HH:mm:ss"
        v-model="startTime"
        placeholder="上班时间点">
      </el-time-picker>
      下班时间：
      <el-time-picker
        value-format="HH:mm:ss"
        v-model="endTime"
        placeholder="下班时间点">
      </el-time-picker>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="isShowDialog = false">
        关闭
      </el-button>
      <el-button type="primary" @click="save">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getPunchInDate, addPunchInDate } from '@/api/oa/oaAttendance-api'
import { parseTime } from '@/utils/index.js'
import { mapGetters } from 'vuex'

export default {
  name: 'oaAttendanceDate',
  components: {},
  props: {
    bizId: {
      type: String,
      default: () => {
        return ''
      }
    },
    name: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      title: '考勤日期',
      isShowDialog: false,
      dayTime: [],
      initDay: [],
      curDate: new Date(),
      startTime: "09:00:00", //上班时间
      endTime: "17:30:00", //下班时间
      firstStartTime: '',
      firstEndTime: ''
    }
  },
  computed: {
    ...mapGetters({
      userId: 'user/userId'
    }),
  },
  watch:{

  },
  created() {

  },
  methods: {
    changeDate(day){
      if( this.dayTime.indexOf(day) < 0){
        this.dayTime.push(day)
      }else{
        this.dayTime.splice(this.dayTime.indexOf(day),1)
      }
    },
    getDateAfterDays(days) {
      const date = new Date(); // 当前日期
      date.setDate(date.getDate() + days); // 当前日期加上365天
      return date.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式的日期
    },
    getPunchInDate() {
      this.dayTime = []
      this.initDay = []
      var myDate = new Date()
      var data = {
        bizId: this.bizId, //项目或标段
        belongStartTime: parseTime(myDate, '{y}-{m}-{d}') , //开始日期
        belongEndTime: this.getDateAfterDays(365) ,//结束日期
        type: "1" //类型 1 上下班  0 休息
      }
      getPunchInDate(data).then(response=>{
        var result = response.result
        this.startTime = parseTime(result[0].startTime, '{h}:{i}:{s}')
        this.endTime = parseTime(result[0].endTime, '{h}:{i}:{s}')
        this.firstStartTime = this.startTime
        this.firstEndTime = this.endTime
        result.forEach(item=>{
          this.dayTime.push(item.belongTime)
          this.initDay.push(item.belongTime)
        })
      })
    },
    showDateDialog() {
      this.isShowDialog = true
      this.getPunchInDate()
    },
    save(){
      var dateInfo = []
      this.dayTime.forEach(item=>{ //遍历选择后日历，没有的加上
        let obj = {
          data: "", //所属日期
          type: 1 //类型 1 上下班，0 休息
        }
        if(this.initDay.indexOf(item) < 0){
          obj.data = item
          obj.type = 1
          dateInfo.push(obj)
        }
      })
      this.initDay.forEach(item=>{//遍历初始化考勤日期，没有在选中的日期时变为休息
        let obj = {
          data: "", //所属日期
          type: 1 //类型 1 上下班，0 休息
        }
        if(this.dayTime.indexOf(item) < 0){
          obj.data = item
          obj.type = 0
          dateInfo.push(obj)
        }
      })
      var data = {
        dateInfo: dateInfo, //日期时间
        bizId: this.bizId, //项目或标段
        startTime: this.startTime, //上班时间
        endTime: this.endTime ,//下班时间
        userId: this.userId,
        createTime: parseTime(new Date())
      }
      if(this.firstStartTime !== this.startTime || this.firstEndTime !== this.endTime){
        var dateInfos = []
        this.dayTime.forEach(item=>{ //遍历选择后日历，没有的加上
          let obj = {
            data: item, //所属日期
            type: 1 //类型 1 上下班，0 休息
          }
          dateInfos.push(obj)
        })
        this.initDay.forEach(item=>{//遍历初始化考勤日期，没有在选中的日期时变为休息
          let obj = {
            data: "", //所属日期
            type: 1 //类型 1 上下班，0 休息
          }
          if(this.dayTime.indexOf(item) < 0){
            obj.data = item
            obj.type = 0
            dateInfos.push(obj)
          }
        })
        data.dateInfo = dateInfos
      }
      addPunchInDate(data).then(response=>{
        const result = response.result.result
        if (result) {
          this.$notify({
            title: '信息',
            message: '保存成功！',
            type: 'success',
            duration: 2000
          })
          this.isShowDialog = false
        } else {
          this.$notify({
            title: '信息',
            message: response.result.info,
            type: 'error',
            duration: 2000
          })
        }
      })
    },
  }
}
</script>
<style scoped lang="scss">
.budge {
  width: 8px;
  height: 8px;
  background: #46a6ff;
  border-radius: 50%;
  margin: 0 auto;
  margin-top: 10px;
}
</style>
<style lang="scss">
  .el-calendar-table .el-calendar-day{
    padding: 0px;
    height: 75px;
  }
</style>
