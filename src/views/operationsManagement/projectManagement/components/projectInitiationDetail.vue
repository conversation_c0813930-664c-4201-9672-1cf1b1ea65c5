<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="项目详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1650px"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-form
          ref="dataForm"
          :inline="true"
          label-position="right"
          label-width="130px"
          :model="formData"
        >
          <el-descriptions
            border
            class="margin-top"
            :column="3"
            :label-style="labelStyle"
            size="medium"
          >
            <el-descriptions-item>
              <template slot="label">单号</template>
              {{ formData.serialNumber }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">登记人</template>
              {{ formData.createByName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">登记部门</template>
              {{ formData.createDepartName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">登记日期</template>
              {{ formData.recordDate }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">项目状态</template>
              {{ formData.projectStatusName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">业务类型</template>
              {{ formData.bizTypeName }}
            </el-descriptions-item>
            <el-descriptions-item :span="3">
              <template slot="label">项目名称</template>
              {{ formData.projectName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">甲方单位</template>
              {{ formData.ownerUnit }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">预计合同金额(元)</template>
              {{ formData.expectedContractAmount | currencyFormat }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">预计开始时间</template>
              {{ formData.estimatedStartTime }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">甲方联系人</template>
              {{ formData.ownerContact }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">甲方联系方式</template>
              {{ formData.ownerContactInformation }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">甲方联系地址</template>
              {{ formData.ownerContactAddress }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="
                formData.projectStatus !==
                '2ddc8e7b-4691-43b7-8260-13d435e3cee6'
              "
            >
              <template slot="label">项目实施部门</template>
              {{ formData.departName }}
            </el-descriptions-item>
            <el-descriptions-item
              v-if="
                formData.projectStatus !==
                '2ddc8e7b-4691-43b7-8260-13d435e3cee6'
              "
            >
              <template slot="label">
                <i class="el-icon-map-location"></i>
                项目位置
              </template>
              {{ formData.siteStart }}
              <el-link
                v-if="formData.siteStart !== ''"
                type="primary"
                @click="showBMap"
              >
                <i class="el-icon-map-location"></i>
              </el-link>
            </el-descriptions-item>
            <el-descriptions-item
              v-if="
                formData.projectStatus !==
                '2ddc8e7b-4691-43b7-8260-13d435e3cee6'
              "
            >
              <template slot="label">
                <i class="el-icon-map-location"></i>
                项目坐标
              </template>
              {{ formData.siteStartX }}
              <el-link
                v-if="formData.siteStartX !== ''"
                type="primary"
                @click="showBMap"
              >
                <i class="el-icon-map-location"></i>
              </el-link>
            </el-descriptions-item>
            <el-descriptions-item :span="3">
              <template slot="label">项目概况</template>
              {{ formData.projectSummary }}
            </el-descriptions-item>
            <el-descriptions-item :span="3">
              <template slot="label">备注</template>
              {{ formData.remark }}
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
        <!-- 附件上传 -->
        <UploadLargeFileFdfsPopupDetail
          ref="UploadLargeFileFdfsPopupDetail"
          style="margin-top: 20px"
        />
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
    <BMapShow ref="BMapShow" />
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  import BMapShow from '@/views/common/BMap/BMapShow.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
      BMapShow,
    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        isApproval: false,
        labelStyle: {
          width: '130px',
        },
      }
    },
    methods: {
      showBMap() {
        let position = this.formData.siteStartX.split(',')
        let marker = {
          position: {
            lng: parseFloat(position[0]),
            lat: parseFloat(position[1]),
          },
          info:
            this.formData.projectName +
            '<br/>' +
            '项目地址：' +
            this.formData.siteStart,
        }
        this.$refs.BMapShow.showBaiDuMap(marker)
      },
      showApprovalFlowByParams(bizKey, row, params) {
        this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(
            row.projectStatus === '348ac237-1cb0-4b44-85fa-961bfb4465e9'
              ? row.settlementId
              : bizKey,
            row,
            params
          )
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      showDialog(row) {
        this.isApproval = false
        this.formData = { ...row }
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.projectStatus ===
                '348ac237-1cb0-4b44-85fa-961bfb4465e9'
                ? this.formData.settlementId
                : this.formData.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
            isShow: true,
          })
        })
        this.dialogDetailVisible = true
      },
    },
  }
</script>

<style lang="scss" scoped></style>
