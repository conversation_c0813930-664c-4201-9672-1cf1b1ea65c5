<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="close"
    :close-on-click-modal="false"
    :title="title"
    top="2vh"
    :visible.sync="dialogFormVisible"
    width="1200px"
    @closed="close"
  >
    <el-table
      ref="tableDataRef"
      v-loading="listLoading"
      border
      :data="list"
      :height="450"
      stripe
      row-key="id"
    >
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.prop === 'serialNumber'">
            <el-link type="primary" @click="toAdjustmentsRecord(row)">{{ row[item.prop] }}</el-link>
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :page-sizes="[5, 10, 20]"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <template #footer>
      <el-button @click="dialogFormVisible = false">关闭</el-button>
    </template>
    <ProjectAdjustmentsDetailPopup
      ref="projectAdjustmentsDetailPopup"
    />
  </el-dialog>
</template>

<script>
  import { getDataListByPage } from '@/api/project/projectAdjustments'
  import ProjectAdjustmentsDetailPopup
    from "@/views/operationsManagement/projectManagement/components/projectAdjustmentsDetail.vue";
  export default {
    components: {ProjectAdjustmentsDetailPopup},
    data() {
      return {
        title: '变更记录',
        listLoading: false,
        dialogFormVisible: false,
        queryForm: {
          projectCode: '',
          projectName: '',
          bizType: '',
          allPermissions: true,
        },
        layout: 'total, sizes, prev, pager, next, jumper',
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 0,
        },
        selectRows: [],
        columns: [
          {
            label: '单号',
            prop: 'serialNumber',
          },
          {
            label: '登记人',
            prop: 'createByName',
          },
          {
            label: '登记部门',
            prop: 'createDepartName',
          },
          {
            label: '登记时间',
            prop: 'createTime',
          }
        ],
        list: [],
      }
    },
    computed: {},
    created() {},
    methods: {
      toAdjustmentsRecord(row) {
        this.$refs.projectAdjustmentsDetailPopup.showDialog(row)
      },
      showDialog(project) {
        this.queryForm.projectId = project.id
        this.title = project.projectName + "-变更记录"
        this.fetchData()
        this.dialogFormVisible = true
      },
      async fetchData() {
        this.queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
          approvalResult: '2',
        }
        this.listLoading = true
        const {
          result: { records, total },
        } = await getDataListByPage(this.queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.fetchData()
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-table__header .el-checkbox {
    display: none;
  }
  .select-content {
    margin-top: 10px;
    .select-list {
      margin-top: 10px;
    }
  }
</style>
