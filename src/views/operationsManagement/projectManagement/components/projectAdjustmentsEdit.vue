<template>
  <el-dialog
    v-drag
    :close-on-click-modal="false"
    append-to-body
    :title="pageTitle"
    :visible.sync="dialogFormVisible"
    width="1400px"
    :before-close="closeBtn"
    center
    top="5vh"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      :rules="rules"
      :model="formData"
      label-position="right"
      label-width="130px"
    >
      <table class="form-table">
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                placeholder="自动生成"
                disabled
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="登记人" prop="createBy">
              <el-input
                v-model="formData.createByName"
                disabled
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="登记部门" prop="createDepart">
              <el-input
                v-model="formData.createDepartName"
                placeholder="自动生成"
                disabled
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="登记日期" prop="recordDate">
              <el-date-picker
                v-model="formData.recordDate"
                type="date"
                style="width: 250px"
                placeholder="请选择登记日期"
              ></el-date-picker>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="formData.projectName"
                placeholder="请输入项目名称"
                style="width: 250px"
                @click.native="projectChange"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="业务类型" prop="bizType">
              <el-select
                v-model="formData.bizType"
                multiple
                style="width: 250px"
                filterable
                placeholder="请选择业务类型"
              >
                <el-option
                  v-for="item in bizTypeList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item
              label="预计合同金额(元)"
              prop="expectedContractAmount"
            >
              <t-input-money
                v-model="formData.expectedContractAmount"
                placeholder="请输入预计合同金额"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="预计开始时间" prop="estimatedStartTime">
              <el-date-picker
                v-model="formData.estimatedStartTime"
                type="date"
                style="width: 250px"
                placeholder="请选择预计开始时间"
              ></el-date-picker>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="甲方单位" prop="ownerUnit">
              <el-input
                v-model="formData.ownerUnit"
                placeholder="请输入甲方单位"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="甲方联系人" prop="ownerContact">
              <el-input
                v-model="formData.ownerContact"
                placeholder="请输入甲方联系人"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="甲方联系方式" prop="ownerContactInformation">
              <el-input
                v-model="formData.ownerContactInformation"
                placeholder="请输入联系方式"
                maxlength="20"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="甲方联系地址" prop="ownerContactAddress">
              <el-input
                v-model="formData.ownerContactAddress"
                placeholder="请输入联系地址"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr v-show="formData.projectStatus === 'c2302242-8143-4ad6-876f-1d38785e1f44'">

          <td>
            <el-form-item label="项目实施部门" prop="depart">
              <el-input
                v-model="formData.departName"
                disabled
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item
              label="项目位置"
              prop="siteStart"
              @click.native="showBMap"
            >
              <el-input
                v-model="formData.siteStart"
                placeholder="请选择项目位置"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item
              label="项目坐标"
              prop="siteStartX"
              @click.native="showBMap"
            >
              <el-input
                v-model="formData.siteStartX"
                placeholder="请选择项目坐标"
                maxlength="11"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="项目概况" prop="projectSummary">
              <el-input
                v-model="formData.projectSummary"
                type="textarea"
                rows="3"
                placeholder="请输入项目概况"
                maxlength="1000"
                style="width: 1150px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                rows="3"
                placeholder="请输入备注"
                maxlength="1000"
                style="width: 1150px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </div>
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <BMapPopup ref="BMapPopup" @callBackSite="callBackSite" />

    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { parseTime, genUUID } from '@/utils/th_utils'
  import { saveData, checkData } from '@/api/project/projectAdjustments'
  import BMapPopup from '@/views/common/BMap/BMapPopup.vue'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import ProjectListPopup from "@/views/common/ProjectListPopup.vue";
  export default {
    computed: {
      ...mapGetters({
        departList: 'user/departList',
        userName: 'user/userName',
        userId: 'user/userId',
      }),
      pageTitle() {
        return this.formData.isAdd ? '项目修正登记' : '项目修正编辑'
      },
    },
    props: {
      departTreeData: {
        type: Array,
        default() {
          return []
        },
      },
      bizTypeList: {
        type: Array,
        default() {
          return []
        },
      },
    },
    components: {
      ProjectListPopup,
      UploadLargeFileFdfsPopup,
      BMapPopup,
    },
    data() {
      const validateOwnerContactInformation = (rule, value, callback) => {
        if (!value) {
          callback()
        }
        const reg = /^[0-9\-]+$/
        if (reg.test(value)) {
          callback()
        }
        callback(new Error('格式输入有误，请输入手机号或者座机号！'))
      }
      return {
        dialogFormVisible: false,
        formLoading: false,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        formData: {},
        rules: {
          recordDate: [
            { required: true, message: '请选择登记日期', trigger: 'change' },
          ],
          bizType: [
            { required: true, message: '请选择业务类型', trigger: 'change' },
          ],
          projectName: [
            { required: true, message: '请输入项目名称', trigger: 'change' },
          ],
          siteStart: [
            { required: true, message: '请选择项目位置', trigger: 'change' },
          ],
          siteStartX: [
            { required: true, message: '请选择项目坐标', trigger: 'change' },
          ],
          ownerContactInformation: [
            { required: true, message: '请输入甲方联系方式', trigger: 'blur' },
            { validator: validateOwnerContactInformation, trigger: 'blur' },
          ],
          ownerUnit:[
            { required: true, message: '请输入甲方单位', trigger: 'blur' },
          ],
          expectedContractAmount:[
            { required: true, message: '请输入预计合同金额', trigger: 'blur' },
          ],
          estimatedStartTime:[
            { required: true, message: '请选择预计开始时间', trigger: 'change' },
          ],
          ownerContact:[
            { required: true, message: '请输入甲方联系人', trigger: 'blur' },
          ],
        },
      }
    },
    methods: {
      // 点击选中项目
      projectChange() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName
        })
      },
      changeProjectStatus(id){
        if(id === '2ddc8e7b-4691-43b7-8260-13d435e3cee6'){ // 跟踪
          this.rules.siteStart[0].required = false;
          this.rules.siteStartX[0].required = false;
        }else{
          this.rules.siteStart[0].required = true;
          this.rules.siteStartX[0].required = true;
        }
      },
      // 获取选中的项目信息
      getProjectInfo(data) {
        if (data.length > 0) {
          const project = data[0]
          this.formData.projectId = project.id
          this.formData.approvalResultType = [0,1]
          checkData(this.formData).then((response) => {
            if (response.result === 200) {
              this.formData.projectStatus = project.projectStatus

              this.changeProjectStatus(this.formData.projectStatus)

              this.formData.depart = project.depart
              this.formData.departCode = project.departCode
              this.formData.departName = project.departName
              this.formData.projectName = project.projectName
              if (this.formData.bizType) {
                this.formData.bizType = project.bizType.split(',')
              }
              this.formData.bizTypeName = project.bizTypeName
              this.formData.ownerUnit = project.ownerUnit
              this.formData.expectedContractAmount = project.expectedContractAmount
              this.formData.expectedContractAmountShow = project.expectedContractAmountShow
              this.formData.estimatedStartTime = project.estimatedStartTime
              this.formData.ownerContact = project.ownerContact
              this.formData.ownerContactInformation = project.ownerContactInformation
              this.formData.ownerContactAddress = project.ownerContactAddress
              this.formData.siteStart = project.siteStart
              this.formData.siteStartX = project.siteStartX
              this.formData.projectSummary = project.projectSummary
              this.formData.remark = project.remark
            } else {
              this.$baseMessage(
                '已存在未走完流程修正数据!',
                'error',
                'vab-hey-message-error'
              )
              return
            }
          })
        }
      },
      async showDialog(row) {
        this.formData = { ...row }
        if (this.formData.isAdd) {
          this.init()
        } else {
          // 编辑时处理业务类型
          if (this.formData.bizType) {
            this.formData.bizType = this.formData.bizType.split(',')
          }
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      init() {
        this.formData = {
          id: genUUID(),
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          recordDate: parseTime(new Date()),
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'XMBG',
          serialNumberTable: 'project_adjustments',
          bizType: [],
          bizTypeName: '',
          projectName: '',
          ownerUnit: '',
          expectedContractAmount: '',
          expectedContractAmountShow: '',
          estimatedStartTime: '',
          ownerContact: '',
          ownerContactInformation: '',
          ownerContactAddress: '',
          siteStart: '',
          siteStartX: '',
          projectSummary: '',
          remark: '',
          isAdd: true,
        }
      },
      closeBtn() {
        this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
        this.init()
      },
      saveBtn() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
              bizType: this.formData.bizType.join(','),
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
      changeDepart(id) {
        for (const item of this.departTreeData) {
          if (item.id === id) {
            this.formData.depart = item.id
            this.formData.departCode = item.departCode
            this.formData.departName = item.departName
          }
        }
      },
      showBMap() {
        this.$refs.BMapPopup.showBMap(this.formData)
      },
      callBackSite(data) {
        this.formData.siteStartX = data[0]
        this.formData.siteStart = data[1]
        this.$forceUpdate()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image-upload {
    position: absolute;
    right: 21px;
    bottom: 93px;
    width: 536px;
    height: 160px;
    background: #fff;
    border-left: 1px solid #bbb;
  }
</style>
