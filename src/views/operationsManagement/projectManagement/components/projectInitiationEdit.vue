<template>
  <el-dialog
    v-drag
    :close-on-click-modal="false"
    append-to-body
    :title="pageTitle"
    :visible.sync="dialogFormVisible"
    width="1400px"
    :before-close="closeBtn"
    center
    top="5vh"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      :rules="rules"
      :model="formData"
      label-position="right"
      label-width="130px"
    >
      <table class="form-table">
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                placeholder="自动生成"
                disabled
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="登记人" prop="createBy">
              <el-input
                v-model="formData.createByName"
                disabled
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="登记部门" prop="createDepart">
              <el-input
                v-model="formData.createDepartName"
                placeholder="自动生成"
                disabled
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="登记日期" prop="recordDate">
              <el-date-picker
                v-model="formData.recordDate"
                type="date"
                style="width: 250px"
                placeholder="请选择登记日期"
              ></el-date-picker>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="项目状态" prop="projectStatus">
              <el-select
                v-model="formData.projectStatus"
                style="width: 250px"
                filterable
                placeholder="请选择项目状态"
                :disabled="!formData.isAdd "
                @change="changeProjectStatus"
              >
                <el-option
                  v-for="item in projectStatusList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                  :disabled="item.dictCode === 'settlement' || item.dictCode === 'devOps' "
                ></el-option>
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="业务类型" prop="bizType">
              <el-select
                v-model="formData.bizType"
                multiple
                style="width: 250px"
                filterable
                placeholder="请选择业务类型"
              >
                <el-option
                  v-for="item in bizTypeList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="formData.projectName"
                style="width: 700px"
                placeholder="请输入项目名称"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="甲方单位" prop="ownerUnit">
              <el-input
                v-model="formData.ownerUnit"
                placeholder="请输入甲方单位"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item
              label="预计合同金额(元)"
              prop="expectedContractAmount"
            >
              <t-input-money
                v-model="formData.expectedContractAmount"
                placeholder="请输入预计合同金额"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="预计开始时间" prop="estimatedStartTime">
              <el-date-picker
                v-model="formData.estimatedStartTime"
                type="date"
                style="width: 250px"
                placeholder="请选择预计开始时间"
              ></el-date-picker>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="甲方联系人" prop="ownerContact">
              <el-input
                v-model="formData.ownerContact"
                placeholder="请输入甲方联系人"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="甲方联系方式" prop="ownerContactInformation">
              <el-input
                v-model="formData.ownerContactInformation"
                placeholder="请输入联系方式"
                maxlength="20"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="甲方联系地址" prop="ownerContactAddress">
              <el-input
                v-model="formData.ownerContactAddress"
                placeholder="请输入联系地址"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr v-show="formData.projectStatus === 'c2302242-8143-4ad6-876f-1d38785e1f44'">
          <td>
            <el-form-item label="项目实施部门" prop="depart" >
              <t-form-clearable-tree
                v-if="dialogFormVisible"
                v-model="formData.depart"
                :treeData="departTreeData"
                :disabled="!formData.isAdd && formData.projectStutus === 'c2302242-8143-4ad6-876f-1d38785e1f44'"
                :defaultProps="{ children: 'children', label: 'departName' }"
                :showTop="false"
                placeholder="请选择项目实施部门"
                @change="changeDepart"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item
              label="项目位置"
              prop="siteStart"
              @click.native="showBMap"
            >
              <el-input
                v-model="formData.siteStart"
                placeholder="请选择项目位置"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
          <td>
            <el-form-item
              label="项目坐标"
              prop="siteStartX"
              @click.native="showBMap"
            >
              <el-input
                v-model="formData.siteStartX"
                placeholder="请选择项目坐标"
                maxlength="11"
                style="width: 250px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="项目概况" prop="projectSummary">
              <el-input
                v-model="formData.projectSummary"
                type="textarea"
                rows="3"
                placeholder="请输入项目概况"
                maxlength="1000"
                style="width: 1150px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                rows="3"
                placeholder="请输入备注"
                maxlength="1000"
                style="width: 1150px"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </div>
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <BMapPopup ref="BMapPopup" @callBackSite="callBackSite" />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { parseTime, genUUID } from '@/utils/th_utils'
  import { saveData, checkData } from '@/api/project'
  import BMapPopup from '@/views/common/BMap/BMapPopup.vue'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import TFormClearableTree from "@/vab/components/TFromClearableTree/index.vue";
  import {resetWorkFlow} from "@/api/workflow/workflow-api";
  export default {
    computed: {
      ...mapGetters({
        departList: 'user/departList',
        userName: 'user/userName',
        userId: 'user/userId',
      }),
      pageTitle() {
        return this.formData.isAdd ? '立项登记' : '立项编辑'
      },
    },
    props: {
      departTreeData: {
        type: Array,
        default() {
          return []
        },
      },
      bizTypeList: {
        type: Array,
        default() {
          return []
        },
      },
      projectStatusList:{
        type: Array,
        default() {
          return []
        },
      }
    },
    components: {
      TFormClearableTree,
      UploadLargeFileFdfsPopup,
      BMapPopup,
    },
    data() {
      const validateProjectName = (rule, value, callback) => {
        const data = {
          isAdd: this.formData.isAdd,
          projectName: this.formData.projectName,
        }
        if (!this.formData.isAdd) {
          data.id = this.formData.id
        }
        checkData(data).then((response) => {
          if (response.result === 200) {
            callback()
          } else {
            callback(new Error('项目名称已存在，请修改!'))
          }
        })
      }
      const validateOwnerContactInformation = (rule, value, callback) => {
        if (!value) {
          callback()
        }
        const reg = /^[0-9\-]+$/
        if (reg.test(value)) {
          callback()
        }
        callback(new Error('格式输入有误，请输入手机号或者座机号！'))
      }
      return {
        dialogFormVisible: false,
        formLoading: false,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        formData: {},
        rules: {
          recordDate: [
            { required: true, message: '请选择登记日期', trigger: 'change' },
          ],
          depart: [
            {
              required: true,
              message: '请选择项目实施部门',
              trigger: 'change',
            },
          ],
          bizType: [
            { required: true, message: '请选择业务类型', trigger: 'change' },
          ],
          projectName: [
            { required: true, message: '请输入项目名称', trigger: 'blur' },
            { validator: validateProjectName, trigger: 'blur' },
          ],
          siteStart: [
            { required: true, message: '请选择项目位置', trigger: 'change' },
          ],
          siteStartX: [
            { required: true, message: '请选择项目坐标', trigger: 'change' },
          ],
          ownerContactInformation: [
            { required: true, message: '请输入甲方联系方式', trigger: 'blur' },
            { validator: validateOwnerContactInformation, trigger: 'blur' },
          ],
          ownerUnit:[
            { required: true, message: '请输入甲方单位', trigger: 'blur' },
          ],
          expectedContractAmount:[
            { required: true, message: '请输入预计合同金额', trigger: 'blur' },
          ],
          estimatedStartTime:[
            { required: true, message: '请选择预计开始时间', trigger: 'change' },
          ],
          ownerContact:[
            { required: true, message: '请输入甲方联系人', trigger: 'blur' },
          ],
          projectStatus: [
            { required: true, message: '请选择项目状态', trigger: 'change' },
          ],
        },
      }
    },
    methods: {
      async showDialog(row) {
        this.formData = { ...row }
        if (this.formData.isAdd) {
          this.init()
          this.formData.projectStatus = row.projectStatus
        } else {
          // 编辑时处理业务类型
          if (this.formData.bizType) {
            this.formData.bizType = this.formData.bizType.split(',')
          }
          this.changeProjectStatus(this.formData.projectStatus)
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      init() {
        this.formData = {
          id: genUUID(),
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          recordDate: parseTime(new Date()),
          depart: '',
          departName: '',
          departCode: '',
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'LXGL',
          serialNumberTable: 'project',
          bizType: [],
          bizTypeName: '',
          projectName: '',
          ownerUnit: '',
          expectedContractAmount: '',
          expectedContractAmountShow: '',
          estimatedStartTime: '',
          ownerContact: '',
          ownerContactInformation: '',
          ownerContactAddress: '',
          siteStart: '',
          siteStartX: '',
          projectSummary: '',
          remark: '',
          isAdd: true,
          projectStatus:''
        }
      },
      closeBtn() {
        this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
        this.init()
      },
      saveBtn() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
              bizType: this.formData.bizType.join(','),
            }
            if(this.formData.projectStatus !== 'c2302242-8143-4ad6-876f-1d38785e1f44') { // 实施
              formData.departCode = this.departList[0].departCode
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                if(formData.deleteFlow){
                  const data = {
                    bizId: formData.id,
                  }
                  this.listLoading = true
                  resetWorkFlow(data).then((response) => {
                    const code = response.code
                    if (code === 200) {
                      this.$baseMessage(
                        '保存成功!',
                        'success',
                        'vab-hey-message-success'
                      )
                      this.$emit('refreshPage')
                      this.formLoading = false
                      this.dialogFormVisible = false
                    } else {
                      this.$baseMessage(
                        response.result.msg,
                        'error',
                        'vab-hey-message-error'
                      )
                    }
                  })
                }else {
                  this.$baseMessage(
                    '保存成功!',
                    'success',
                    'vab-hey-message-success'
                  )
                  this.$emit('refreshPage')
                  this.formLoading = false
                  this.dialogFormVisible = false
                }
              }

            })
          }
        })
      },
      changeProjectStatus(id){
        if(id === '2ddc8e7b-4691-43b7-8260-13d435e3cee6'){ // 跟踪
          this.rules.depart[0].required = false;
          this.rules.siteStart[0].required = false;
          this.rules.siteStartX[0].required = false;
        }else{
          this.rules.depart[0].required = true;
          this.rules.siteStart[0].required = true;
          this.rules.siteStartX[0].required = true;
        }
      },
      changeDepart(id) {
        for (const item of this.departTreeData) {
          if (item.id === id) {
            this.formData.depart = item.id
            this.formData.departCode = item.departCode
            this.formData.departName = item.departName
          }
        }
      },
      showBMap() {
        this.$refs.BMapPopup.showBMap(this.formData)
      },
      callBackSite(data) {
        this.formData.siteStartX = data[0]
        this.formData.siteStart = data[1]
        this.$forceUpdate()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image-upload {
    position: absolute;
    right: 21px;
    bottom: 93px;
    width: 536px;
    height: 160px;
    background: #fff;
    border-left: 1px solid #bbb;
  }
</style>
