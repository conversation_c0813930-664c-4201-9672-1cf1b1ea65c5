<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <el-tabs v-model="activeTabName" @tab-click="handleClickTab">
      <!-- <el-tab-pane label="全部项目" name="all"></el-tab-pane>
      <el-tab-pane label="跟踪项目" name="track"></el-tab-pane>
      <el-tab-pane label="实施项目" name="implement"></el-tab-pane>
      <el-tab-pane label="结算项目" name="settlement"></el-tab-pane>
      <el-tab-pane label="运维项目" name="devOps"></el-tab-pane> -->
      <el-tab-pane v-for="(item, index) in tabNameList" :label="item.label+'('+item.count+')'" :name="item.name" :key="index"></el-tab-pane>
    </el-tabs>
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="queryForm.projectName"
              clearable
              placeholder="请输入项目名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="登记部门" prop="createDepartName">
            <el-input
              v-model="queryForm.createDepartName"
              clearable
              placeholder="请输入登记部门"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="业务类型" prop="bizType">
            <el-select
              v-model="queryForm.bizType"
              clearable
              filterable
              placeholder="请选择业务类型"
              style="width: 200px"
            >
              <el-option
                v-for="(item, index) in bizTypeList"
                :key="index"
                :label="item.dictName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="甲方单位" prop="ownerUnit">
            <el-input
              v-model="queryForm.ownerUnit"
              clearable
              placeholder="请输入甲方单位"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item v-show="!fold" label="实施部门" prop="departName">
            <el-input
              v-model="queryForm.departName"
              clearable
              placeholder="请输入实施部门"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item v-show="!fold" label="登记人" prop="createByName">
            <el-input
              v-model="queryForm.createByName"
              clearable
              placeholder="请输入登记人"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item v-show="!fold" label="审批状态" prop="approvalResult">
            <el-select
              ref="selectFlow"
              v-model="queryForm.approvalResult"
              filterable
              placeholder="请选择审批状态"
              style="width: 200px"
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in approvalResultNameList"
                :key="item.id"
                :label="item.approvalResultName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
          </el-form-item>
          <el-button
            style="margin: 0 0 0 10px !important"
            type="text"
            @click="handleFold"
          >
            <span v-if="fold">展开</span>
            <span v-else>合并</span>
            <vab-icon
              class="vab-dropdown"
              :class="{ 'vab-dropdown-active': fold }"
              icon="arrow-up-s-line"
            />
          </el-button>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          v-permissions="{ permission: ['projectInitiation:add'] }"
          icon="el-icon-plus"
          type="primary"
          :disabled="this.queryForm.projectStatus === '348ac237-1cb0-4b44-85fa-961bfb4465e9' || this.queryForm.projectStatus === '80042b9f-72bd-4249-b321-48f2dfe4db2b'"
          @click="handleAdd"
        >
          添加
        </el-button>
        <el-button
          :disabled="exportLoading"
          icon="el-icon-download"
          type="warning"
          @click.native="exportBtn('项目立项.xlsx')"
        >
          导出
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else-if="item.label === '预计合同金额(元)'">
            {{ row[item.prop] | currencyFormat }}
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['projectInitiation:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="warning"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['projectInitiation:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
              <el-dropdown-item
                divided
                @click.native.prevent="
                  showReport(
                    row,
                    '/ReportServer?reportlet=/oaNew/project/projectInfo.cpt'
                  )
                "
              >
                <i class="el-icon-s-order" />
                查看报表
              </el-dropdown-item>
              <el-dropdown-item @click.native.prevent="handleAdjustments(row)">
                <i class="el-icon-document" />
                变更记录
              </el-dropdown-item>
              <el-dropdown-item
                v-if="row.projectStatus ==='2ddc8e7b-4691-43b7-8260-13d435e3cee6' && row.approvalResult === '2' "
                @click.native.prevent="updateProjectStatus(row)"
              >
                <i class="el-icon-unlock" />
                跟踪转实施
              </el-dropdown-item>
              <el-dropdown-item
                v-if="row.projectStatus ==='c2302242-8143-4ad6-876f-1d38785e1f44' && row.approvalResult === '2' "
                @click.native.prevent="updateProjectStatusToSettlement(row)"
              >
                <i class="el-icon-unlock" />
                实施转结算
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <!-- 详情 -->
    <tableDetail ref="tableDetail" />
    <!-- 新增修改 -->
    <tableEdit
      ref="tableEdit"
      :biz-type-list="bizTypeList"
      :project-status-list="projectStatusList"
      :depart-tree-data="allDepartList"
      @refreshPage="resetFormPage"
    />
    <settlement-edit ref="settlementEdit" @fetch-data="fetchData" />
    <ProjectAdjustmentsListPopup ref="projectAdjustmentsListPopup" />
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />
    <VabTableExport ref="tableExport" />
  </div>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import tableDetail from './components/projectInitiationDetail.vue'
  import tableEdit from './components/projectInitiationEdit.vue'
  import settlementEdit from '@/views/financialManagement/settlementManagement/components/settlementEdit.vue'
  import { getDictList } from '@/api/system/dict-api'
  import {getDataListByPage, deleteData, exportData, saveData, queryProjectStatusCount, checkData} from '@/api/project'
  import PurchaseApplicationDetail from '@/views/approvalManagement/purchasingManagement/components/purchaseApplicationDetail.vue'
  import ProjectAdjustmentsListPopup from '@/views/operationsManagement/projectManagement/components/ProjectAdjustmentsListPopup.vue'
  import {getDoBudgetProjectIdList} from "@/api/financialManagement/budgetManagement";
  import { getDataList } from '@/api/financialManagement/settlementManagement'
  export default {
    name: 'ProjectInitiation',
    components: {
      ProjectAdjustmentsListPopup,
      PurchaseApplicationDetail,
      tableDetail,
      tableEdit,
      settlementEdit,
    },
    mixins: [tableMix],
    data() {
      return {
        formMinHeight: 2,
        formMaxHeight: 2,
        columns: [
          {
            label: '单号',
            prop: 'serialNumber',
            width: '250',
          },
          {
            label: '项目名称',
            prop: 'projectName',
            width: '320',
          },
          {
            label: '登记人',
            prop: 'createByName',
          },
          {
            label: '登记部门',
            prop: 'createDepartName',
            width: '120',
          },
          {
            label: '登记日期',
            prop: 'recordDate',
            width: '120',
          },
          {
            label: '项目状态',
            prop: 'projectStatusName',
            width: '120',
          },
          {
            label: '项目实施部门',
            prop: 'departName',
            width: '120',
          },
          {
            label: '业务类型',
            prop: 'bizTypeName',
            width: '120',
          },
          {
            label: '甲方单位',
            prop: 'ownerUnit',
            width: '260',
          },
          {
            label: '预计合同金额(元)',
            prop: 'expectedContractAmount',
            width: '140',
          },
          {
            label: '预计开始时间',
            prop: 'estimatedStartTime',
            width: '120',
          },
          {
            label: '审批状态',
            prop: 'approvalResultName',
            width: '120',
          },
        ],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        queryForm: {
          serialNumber: '',
          projectName: '',
          createByName: '',
          createDepartName: '',
          departName: '',
          bizType: '',
          ownerUnit: '',
          projectStatus:''
        },
        bizTypeList: [],
        projectStatusList: [],
        activeTabName:'all',
        tabNameList:[{
          label:'全部项目',
          name:'all',
          count: 0
        },{
          label:'跟踪项目',
          name:'track',
          count: 0
        },{
          label:'实施项目',
          name:'implement',
          count: 0
        },{
          label:'结算项目',
          name:'settlement',
          count: 0
        },{
          label:'运维项目',
          name:'devOps',
          count: 0
        }],
      }
    },
    created() {
      this.getDictListByCodes()
      this.fetchData()
    },
    methods: {
      queryProjectStatusCount(){
        queryProjectStatusCount({}).then(res=>{
          var count = 0
          res.result.forEach(item=>{
            count += Number(item.count)
            this.tabNameList.forEach(val=>{
              if(val.name === item.dictCode){
                val.count = item.count
              }
            })
          })
          this.tabNameList[0].count = count
        })
      },
      handleClickTab(tab, event) {
        if(tab.name!=='all'){
          this.queryForm.projectStatus = this.projectStatusList.find(item=>item.dictCode===tab.name).id
        }else{
          this.queryForm.projectStatus = ''
        }
        this.fetchData()
      },
      //查看变更记录
      handleAdjustments(row) {
        this.$refs.projectAdjustmentsListPopup.showDialog(row)
      },
      updateProjectStatusToSettlement(row){
        const data = {
          projectStatus: "c2302242-8143-4ad6-876f-1d38785e1f44",
          id: row.id,
          isAdd:true
        }
        checkData(data).then((response) => {
          if (response.result !== 200) {
            getDoBudgetProjectIdList({}).then((res) => {
              this.projectIdList = res.result
              if (this.projectIdList.includes(row.id)) {
                const queryForm = {
                  dataType: 'budget',
                  projectId :row.id
                }
                getDataList(queryForm).then(res => {
                  if(res.result.length > 0){
                    this.$baseMessage('当前项目数据已做结算!', 'error', 'vab-hey-message-error')
                  }else{
                    this.$baseConfirm('你确定要将当前数据转结算吗', null, async () => {
                      this.$refs['settlementEdit'].projectInfoShow(row)
                    })
                  }
                })
              }else {
                this.$baseMessage('当前未做预算无法结算!', 'error', 'vab-hey-message-error')
              }
            })
          } else {
            this.$baseMessage('当前数据状态不是实施，请刷新!', 'error', 'vab-hey-message-error')
          }
        })
      },
      updateProjectStatus(row){
        const data = {
          projectStatus: "2ddc8e7b-4691-43b7-8260-13d435e3cee6",
          id: row.id,
          isAdd:true
        }
        checkData(data).then((response) => {
          if (response.result !== 200) {
            this.$baseConfirm('你确定要将当前数据转实施吗', null, async () => {
              // 清空当前流程并修改该项目状态为实施
              row.projectStatus = 'c2302242-8143-4ad6-876f-1d38785e1f44'
              row.deleteFlow  = true
              this.$refs['tableEdit'].showDialog({
                isAdd: false,
                ...row,
              })
            })
          } else {
            this.$baseMessage('当前数据状态不是跟踪，请刷新!', 'error', 'vab-hey-message-error')
          }
        })
      },
      fetchData() {
        this.listLoading = true
        this.queryProjectStatusCount()
        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },
      // 添加
      handleAdd() {
        this.$refs['tableEdit'].showDialog({ isAdd: true,projectStatus:this.queryForm.projectStatus })
      },
      // 编辑
      handleEdit(row) {
        this.$refs['tableEdit'].showDialog({
          isAdd: false,
          ...row,
        })
      },
      //详情
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      // 删除
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteData({ id: row.id })
              .then(() => {
                this.pageInfo.curPage = 1
                this.batchDeleteImg(row.id, this.$route.name)
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportData({ ...this.queryForm, ...columns }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },
      // 获取数据字典
      getDictListByCodes() {
        getDictList({ dictCode: 'projectBizType,projectStatus' }).then((res) => {
          res.result.forEach((item) => {
            if (item.dictCode === 'projectBizType') {
              this.bizTypeList = item.children
            }
            if (item.dictCode === 'projectStatus') {
              this.projectStatusList = item.children
            }
          })
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
