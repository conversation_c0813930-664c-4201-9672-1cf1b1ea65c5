<template>
  <el-dialog
    v-drag
    :close-on-click-modal="false"
    append-to-body
    :title="pageTitle"
    :visible.sync="dialogFormVisible"
    width="1420px"
:before-close="closeBtn"
    top="5vh"
    center
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-form
        ref="dataForm"
        v-loading="formLoading"
        :inline="true"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="140px"
      >
        <table class="form-table">
          <tr class="title">
            <td colspan="3">基本信息</td>
          </tr>
          <tr>
            <td>
              <el-form-item label="单号" prop="serialNumber">
                <el-input
                  v-model="formData.serialNumber"
                  placeholder="自动生成"
                  disabled
                  style="width: 250px"
                ></el-input>
              </el-form-item>
            </td>
            <td>
              <el-form-item label="登记人" prop="createBy">
                <el-input
                  v-model="formData.createByName"
                  disabled
                  style="width: 250px"
                ></el-input>
              </el-form-item>
            </td>
            <td>
              <el-form-item label="登记部门" prop="createDepart">
                <el-input
                  v-model="formData.createDepartName"
                  placeholder="自动生成"
                  disabled
                  style="width: 250px"
                ></el-input>
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="登记日期" prop="recordDate">
                <el-date-picker
                  v-model="formData.recordDate"
                  type="date"
                  placeholder="请选择登记日期"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="公司类型" prop="clienteleType">
                <el-select
                  v-model="formData.clienteleType"
                  placeholder="请选择公司类型"
                >
                  <el-option
                    v-for="item in clienteleTypeList"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </td>
            <td>
              <el-form-item label="所属行业" prop="industry">
                <el-select
                  v-model="formData.industry"
                  style="width: 250px"
                  placeholder="请选择所属行业"
                >
                  <el-option
                    v-for="item in industryList"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="公司名称" prop="clienteleName">
                <el-input
                  v-model="formData.clienteleName"
                  placeholder="请输入公司名称"
                  @change="handleClienteleNameChange"
                  style="width: 1158px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="客户联系人" prop="customerContact">
                <el-input
                  v-model="formData.customerContact"
                  placeholder="请输入客户联系人"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="客户职务" prop="customerPost">
                <el-input
                  v-model="formData.customerPost"
                  placeholder="请输入客户职务"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item
                label="客户联系方式"
                prop="customerContactInformation"
              >
                <el-input
                  v-model="formData.customerContactInformation"
                  placeholder="请输入客户联系方式"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="办公地址" prop="officeAddress">
                <el-input
                  v-model="formData.officeAddress"
                  placeholder="请输入办公地址"
                  style="width: 1158px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr class="title">
            <td colspan="3">开票信息</td>
          </tr>
          <tr>
            <td colspan="2">
              <el-form-item label="开户名" prop="accountName">
                <el-input
                  v-model="formData.accountName"
                  placeholder="自动关联"
                  disabled
                  style="width: 715px"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="税号" prop="taxId">
                <el-input
                  v-model="formData.taxId"
                  placeholder="请输入税号"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="2">
              <el-form-item label="注册地址" prop="companyAddress">
                <el-input
                  v-model="formData.companyAddress"
                  placeholder="请输入注册地址"
                  style="width: 715px"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="电话" prop="phone">
                <el-input
                  v-model="formData.phone"
                  style="width: 250px"
                  placeholder="请输入电话"
                />
              </el-form-item>
            </td>
          </tr>

          <tr>
            <td colspan="2">
              <el-form-item label="开户银行" prop="bank">
                <el-input
                  v-model="formData.bank"
                  placeholder="请输入开户银行"
                  style="width: 715px"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="银行账户" prop="bankAccount">
                <el-input
                  v-model="formData.bankAccount"
                  placeholder="请输入银行账户"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  placeholder="请输入备注"
                  maxlength="1000"
                  style="width: 1158px"
                ></el-input>
              </el-form-item>
            </td>
          </tr>
        </table>
        <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
        <!-- <HistoricalVersionsPopup ref="HistoricalVersionsPopup" /> -->
      </el-form>
    </div>
    <template #footer>
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import {
    saveData,
    checkData,
  } from '@/api/marketManagement/clienteleManagement'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
    import HistoricalVersionsPopup from '@/views/operationsManagement/cooperativePartner/common/HistoricalVersionsPopup.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopup,
      HistoricalVersionsPopup,
    },
    props: {
      clienteleTypeList: {
        type: Array,
        default() {
          return []
        },
      },
      industryList: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
      const validatePhone = (rule, value, callback) => {
        if (!value) {
          callback()
        }
        const reg = /^[0-9\-]+$/
        if (reg.test(value)) {
          callback()
        }
        callback(new Error('格式输入有误，请输入手机号或者座机号！'))
      }
      const validateClienteleName = (rule, value, callback) => {
        const data = {
          isAdd: this.formData.isAdd,
          clienteleName: this.formData.clienteleName,
          isNewVersion:'0',
        }
        if (!this.formData.isAdd) {
          data.id = this.formData.id
        }
        if (this.formData.isNewVersion === '1') {
          data.id = this.formData.oldId
        }
        checkData(data).then((response) => {
          if (response.result === 200) {
            callback()
          } else {
            callback(new Error('公司名称已存在，请修改!'))
          }
        })
      }
      return {
        dialogFormVisible: false,
        formLoading: false,
        formData: {},
        rules: {
          recordDate: [
            { required: true, message: '登记日期为必选', trigger: 'change' },
          ],
          clienteleName: [
            { required: true, message: '公司名称为必填', trigger: 'blur' },
            { validator: validateClienteleName, trigger: 'blur' },
          ],
          clienteleType: [
            { required: true, message: '公司类型为必选', trigger: 'change' },
          ],
          accountName: [
            { required: true, message: '开户名为必填', trigger: 'blur' },
          ],
          taxId: [{ required: true, message: '税号为必填', trigger: 'blur' }],
          industry: [
            { required: true, message: '所属行业为必填', trigger: 'blur' },
          ],
          companyAddress: [
            { required: true, message: '注册地址为必填', trigger: 'blur' },
          ],
          phone: [{ validator: validatePhone, trigger: 'blur' }],
          customerContactInformation: [
            { validator: validatePhone, trigger: 'blur' },
          ],
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增客户' : '编辑客户'
      },
    },
    methods: {
      async showDialog(row) {
        this.formData = { ...row }
        if (this.formData.isAdd) {
          this.init()
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'customerManagement',
          })
          //  this.$refs.HistoricalVersionsPopup.getParamsData({
          //      newId: this.formData.newId,
          //      version:this.formData.version,
          // })
          //    noSelectId:this.formData.id,
        })
      },
      init() {
        // // this.$parent.$refs.UploadLargeFileFdfsPopup.headRun()
        const newId = genUUID()
        this.formData = {
          id: newId,
          newId:newId,
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          recordDate: parseTime(new Date()),
          depart: this.departList[0].id,
          departName: this.departList[0].departName,
          departCode: this.departList[0].departCode,
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'KHGL',
          serialNumberTable: 'clientele_management',
          clienteleType: '',
          clienteleName: '',
          industry: '',
          customerContact: '',
          customerPost: '',
          customerContactInformation: '',
          officeAddress: '',
          accountName: '',
          phone: '',
          taxId: '',
          bank: '',
          bankAccount: '',
          companyAddress: '',
          remark: '',
          isAdd: true,
          version: 1,
        }
      },
      closeBtn() {
        this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
        this.init()
      },
      saveBtn() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
      handleClienteleNameChange() {
        this.formData.accountName = this.formData.clienteleName
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
