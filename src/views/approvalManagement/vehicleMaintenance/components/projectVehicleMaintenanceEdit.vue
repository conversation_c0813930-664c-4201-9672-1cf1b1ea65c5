<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="close"
    center
    :close-on-click-modal="false"
    :title="pageTitle"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1420px"
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-form
        ref="dataForm"
        v-loading="formLoading"
        :inline="true"
        label-position="right"
        label-width="140px"
        :model="formData"
        :rules="rules"
      >
        <table class="form-table">
          <tr class="title">
            <td colspan="3">项目车辆维保</td>
          </tr>
          <tr>
            <td>
              <el-form-item label="编号" prop="serialNumber">
                <el-input
                  v-model="formData.serialNumber"
                  disabled
                  placeholder="自动生成"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="申请人" prop="createBy">
                <el-input
                  v-model="formData.createByName"
                  disabled
                  style="width: 250px"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="申请部门" prop="createDepart">
                <el-input
                  v-model="formData.createDepartName"
                  disabled
                  placeholder="自动生成"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="所属项目" prop="projectId">
                <el-input
                  v-model="formData.projectName"
                  placeholder="自动关联"
                  style="width: 250px"
                  @click.native="selectProject"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="所属部门" prop="maintenanceDepart">
                <t-form-tree
                  v-model="formData.maintenanceDepart"
                  :default-props="{
                    children: 'children',
                    label: 'departName',
                  }"
                  :disabled="true"
                  :show-top="false"
                  :tree-data="departTreeData"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="申请日期" prop="applicationDate">
                <el-date-picker
                  v-model="formData.applicationDate"
                  placeholder="请选择登记日期"
                  style="width: 250px"
                  type="date"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="车牌号" prop="carPlateNumber">
                <el-input
                  placeholder="请选择车牌号"
                  readonly
                  :value="formData.plateNumber"
                  @click.native="selectPlateNumber"
                />
              </el-form-item>
            </td>
            <td dcolspan="1">
              <el-form-item label="公里数(km)" prop="kilometres">
                <el-input
                  v-model="formData.kilometres"
                  placeholder="请输入公里数"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
            <td colspan="1">
              <el-form-item label="预计维保时间" prop="maintenanceTime">
                <el-date-picker
                  v-model="formData.maintenanceTime"
                  placeholder="请选择登记日期"
                  style="width: 250px"
                  type="date"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="维保原因" prop="maintenanceReason">
                <el-input
                  v-model="formData.maintenanceReason"
                  maxlength="1000"
                  placeholder="请输入维保原因"
                  :rows="2"
                  style="width: 1165px"
                  type="textarea"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formData.remark"
                  maxlength="1000"
                  placeholder="请输入内容"
                  :rows="2"
                  style="width: 1165px"
                  type="textarea"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="关联车辆" prop="remark">
                <el-link
                  type="primary"
                  @click="showDetails(formData.carPlateNumber)"
                >
                  {{ formData.applyNo }}
                </el-link>
                <span>&nbsp;&nbsp;</span>
              </el-form-item>
            </td>
          </tr>
        </table>
      </el-form>
      <!--      项目选择  -->
      <ProjectListPopup
        ref="projectListPopupRef"
        @getProjectInfo="changeProject"
      />
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <VehicleListPopup
      ref="VehicleListPopup"
      @getVehicleInfo="changeCarPlateNumber"
    />
    <vehicle-registration-detail
      v-if="showVehicleMaintenaanceDetail"
      ref="vehicleRegistrationDetail"
      :output-volume-list="outputVolumeList"
      :vehicle-attribute-list="vehicleAttributeList"
    />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { saveData } from '@/api/approvalManagement/vehicleMaintenance-api'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'
  import VehicleListPopup from '@/views/common/VehicleListPopup.vue'
  import { getData } from '@/api/administrativeManagement/vehicleRegistration'
  import { constantsExpose } from '@/utils/constantsExpose'
  import VehicleRegistrationDetail from '@/views/administrativeManagement/vehicleManagement/components/vehicleRegistrationDetail.vue'
  import { getDictList } from '@/api/system/dict-api'

  export default {
    components: {
      VehicleRegistrationDetail,
      ProjectListPopup,
      UploadLargeFileFdfsPopup,
      VehicleListPopup,
    },
    props: {
      departTreeData: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        dialogFormVisible: false,
        formLoading: false,
        formData: {},
        dialogStatus: '',
        showVehicleMaintenaanceDetail: false,
        rules: {
          maintenanceDepart: [
            { required: true, message: '所属部门必填', trigger: 'change' },
          ],
          carPlateNumber: [
            { required: true, message: '车牌号为必填', trigger: 'change' },
          ],
          applicationDate: [
            { required: true, message: '申请日期为必填', trigger: 'blur' },
          ],
          projectId: [
            { required: true, message: '项目为必填', trigger: 'change' },
          ],
          kilometres: [
            { required: true, message: '公里数为必填', trigger: 'blur' },
          ],
          maintenanceTime: [
            { required: true, message: '维保时间为必填', trigger: 'blur' },
          ],
          maintenanceReason: [
            { required: true, message: '维保原因为必填', trigger: 'blur' },
          ],
        },
        outputVolumeList: [
          { label: '1.8及以上', value: '1', subsidyStandard: '1.6元/公里' },
          { label: '1.8以下', value: '2', subsidyStandard: '1.1元/公里' },
        ],
        vehicleAttributeList: [], //车辆属性
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增项目车辆维保' : '编辑项目车辆维保'
      },
    },
    created() {
      this.getDictDetailsByCode()
    },
    methods: {
      // 获取业务类型contractType
      getDictDetailsByCode() {
        getDictList({
          dictCode: 'vehicleAttribute',
        }).then((res) => {
          const { result } = res
          this.vehicleAttributeList = result[0].children.map((item) => {
            return {
              label: item.dictName,
              value: item.id,
            }
          })
        })
      },
      async showDetails(id) {
        this.showVehicleMaintenaanceDetail = true
        await getData({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.vehicleRegistrationDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      selectPlateNumber() {
        const attributeList = this.vehicleAttributeList.filter(item => item.label !== '私车公用').map(item => item.value)
        const queryMap = {
          stateList:[0,1],
          attributeList,
        }
        let selectRows = []
        if(this.formData.carPlateNumber) {
          selectRows.push({
            id: this.formData.carPlateNumber,
            plateNumber: this.formData.plateNumber,
          })
        }
        this.$refs.VehicleListPopup.showDialog({
          queryMap,
          selectRows,
          selectIds: this.formData.carPlateNumber,
        })
      },
      //车牌号回调
      async changeCarPlateNumber(data) {
        if (data.length > 0) {
          const item = data[0]
          this.formData.carPlateNumber = item.id
          this.formData.plateNumber = item.plateNumber
          this.formData.applyNo = item.serialNumber
        }
      },
      // 切换项目
      changeProject(data) {
        if (data.length > 0 && data[0].id !== this.formData.projectId) {
          const project = data[0]
          this.formData.projectId = project.id
          this.formData.projectName = project.projectName
          this.formData.maintenanceDepart = project.depart
        }
      },
      // 选择项目
      selectProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName
        })
      },
      async showDialog(row) {
        this.formData = { ...row }
        if (this.formData.isAdd) {
          this.init()
          this.dialogStatus = 'create'
        } else {
          this.dialogStatus = 'update'
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      // 初始化表单
      init() {
        this.formData = {
          id: genUUID(),
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          createTime: parseTime(new Date()),
          depart: this.departList[0].id,
          departName: this.departList[0].departName,
          departCode: this.departList[0].departCode,
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'XMCLWB',
          serialNumberTable: 'vehicle_maintenance',
          applicationDate: parseTime(new Date()),
          maintenanceDepart: '',
          carPlateNumber: '',
          plateNumber: '',
          type: 'project',
          kilometres: '',
          maintenanceTime: parseTime(new Date()),
          maintenanceReason: '',
          projectId: '',
          projectName: '',
          isAdd: true,
        }
      },
      save() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
      close() {
        this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        this.formLoading = false
        this.dialogFormVisible = false
        console.log(this.dialogFormVisible)
        this.$refs.dataForm.resetFields()
        this.init()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
