<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="actionMap[dialogStatus]"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="120px"
      :model="formData"
      :rules="rules"
    >
      <table v-loading="formloading" class="form-table">
        <tr class="title">
          <td colspan="3">基本信息</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请人" prop="createByName">
              <el-input
                v-model="formData.createByName"
                disabled
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请日期" prop="applicationDate">
              <el-date-picker
                v-model="formData.applicationDate"
                disabled
                format="yyyy-MM-dd"
                placeholder="选择时间"
                style="width: 280px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <el-form-item label="请假原因" prop="reasons">
              <el-input
                v-model="formData.reasons"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 720px"
                type="textarea"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="是否是项目" prop="isProject">
              <div class="isOilCard-box">
                <el-radio-group
                  v-model="formData.isProject"
                  :disabled="!formData.isAdd"
                  @input="changeIsPorject"
                >
                  <el-radio :label="0">是</el-radio>
                  <el-radio :label="1">否</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td v-if="formData.type === 'project'" colspan="3">
            <el-form-item label="所属项目" prop="remarks">
              <el-input
                v-model="formData.projectName"
                :disabled="!formData.isAdd"
                filterable
                placeholder="请选择"
                style="width: 720px"
                @click.native="selectContractProject"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="代班人" prop="substitute">
              <t-form-user
                v-model="formData.substitute"
                multiple
                placeholder="请选择"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="请假类型" prop="leaveType">
              <template>
                <el-select
                  v-model="formData.leaveType"
                  placeholder="请选择"
                  style="width: 280px"
                >
                  <el-option
                    v-for="item in selectLeaveType"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  />
                </el-select>
              </template>
            </el-form-item>
          </td>
          <td>
            <el-form-item
              :disabled="IsDepartDisabled"
              label="费用所属部门"
              prop="depart"
            >
              <t-form-tree
                v-model="formData.depart"
                :default-props="{ children: 'children', label: 'departName' }"
                :disabled="!formData.isAdd || formData.type === 'project'"
                :show-top="false"
                :tree-data="departTreeData"
              />
            </el-form-item>
          </td>
        </tr>

        <tr>
          <td>
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="开始时间"
                style="width: 280px"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="getTimeLineDatas"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="结束时间"
                style="width: 280px"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="getTimeLineDatas"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="时长" prop="duration">
              <el-input
                v-model="formData.duration"
                disabled
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>

        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="formData.remarks"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 1180px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label=" 上传照片">
              <div class="form-item-container">
                <div class="item-top">
                  <span>
                    可添加图片或直接上传图片作为封面，上传图片大小不超过10M，类型为jpg、png
                  </span>
                </div>

                <div class="item-radio-btn" style="margin-top: 10px">
                  <el-button icon="el-icon-upload2" @click="showUploadItem()">
                    直接上传
                  </el-button>
                </div>
                <div v-if="formData.picture" class="select-list">
                  <div class="select-item">
                    <el-image
                      :preview-src-list="[formData.picture]"
                      :src="formData.picture"
                      style="width: 40px; height: 40px"
                    />
                    <i
                      class="el-icon-delete"
                      @click="formData.picture = ''"
                    ></i>
                  </div>
                </div>
              </div>
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>

    <!-- 附件上传 -->
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <UploadItem ref="uploadItemRef" @callbackSuccess="callbackSuccess" />
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="saveData">确认</el-button>
    </template>
    <UserCopyPopup ref="UserCopyPopup" @callBackUser="callBackUser" />
  </el-dialog>
</template>

<script>
  import { genUUID } from '@/utils/th_utils.js'
  import { getDictList } from '@/api/system/dict-api'
  import {
    saveData,
    getTimeLineData,
  } from '@/api/administrativeManagement/compensatoryLeave-api'
  import { mapGetters } from 'vuex'
  import UserCopyPopup from '../../administrativeManagement/approvalManagement/common/UserCopyPopup.vue'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import UploadItem from '../../administrativeManagement/announcementManagement/UploadItem.vue'
  import { getDepartList } from '@/api/system/depart-api'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'
  import { getAllPersonLeaveRecordTime } from '@/api/oa/oaAttendance-api'

  export default {
    name: 'TableEdit',
    components: {
      UserCopyPopup,
      UploadLargeFileFdfsPopup,
      UploadItem,
      ProjectListPopup,
    },
    props: {},
    data() {
      const validateDate = (rule, value, callback) => {
        let startDate = Date.parse(this.formData.startTime)
        let endDate = Date.parse(this.formData.endTime)
        if (endDate && endDate && startDate > endDate) {
          this.formData.duration = '自动计算'
          callback(new Error('结束时间不能小于开始时间，请修改!'))
        }
        callback()
      }
      return {
        title: '',
        formloading: false,
        visible: false,
        selectLeaveType: [],
        departTreeData: [],
        dialogStatus: '',
        dialogFormVisible: false,
        IsDepartDisabled: true,
        workStartHour: 9, // 上班时间（小时）
        workEndHour: 17.5, // 下班时间（小时），17.5表示17点30分
        millisecond: null,
        actionMap: {
          update: '编辑请假申请信息',
          create: '新增请假申请信息',
        },
        formData: {},
        rules: {
          reasons: {
            required: true,
            message: '请假原因为必填',
            trigger: 'blur',
          },
          leaveType: {
            required: true,
            message: '请假原类型为必填',
            trigger: 'blur',
          },
          // substitute: { required: true, message: '代班人为必填', trigger: 'blur' },
          startTime: [
            { required: true, message: '请选择开始时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
          endTime: [
            { required: true, message: '请选择结束时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
    },
    created() {
      this.getDictDetailsByCode()
      this.getDepartList()
    },
    methods: {
      getTimeLineDatas() {
        if (this.formData.startTime && this.formData.endTime) {
          const params = {
            startTime: this.formData.startTime,
            endTime: this.formData.endTime,
            userId: this.userId,
            departId: this.formData.depart,
          }
          getTimeLineData(params)
            .then((res) => {
              this.millisecond = res.result
              if (this.millisecond === null) {
                this.calculateDifference()
              } else {
                this.formatDateMillisecond(this.millisecond)
              }
              console.log(res)
            })
            .catch((err) => {
              console.error(err)
              this.calculateDifference()
            })
        } else {
          this.formData.duration = null
        }
      },
      async getProjectInfo(data) {
        console.log(data, 'data')
        if (data.length > 0) {
          const dataObj = data[0]
          const { id, projectName, serialNumber } = dataObj
          if (this.formData.budgetId && id !== this.formData.budgetId) {
            this.formData.budgetId = ''
            this.detailList = []
          }
          this.formData = {
            ...this.formData,
            projectId: id,
            projectName,
            projectSerialNumber: serialNumber,
          }
          this.formData.depart = dataObj.depart
          this.formData.departName = dataObj.departName
          console.log(this.formData.depart, 'depart')
          this.$refs.dataForm.clearValidate('projectName')
        }
      },
      selectContractProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName,
        })
      },
      changeIsPorject() {
        if (this.formData.isProject === 1) {
          this.formData.type = 'depart'
          this.IsDepartDisabled = false
        } else {
          this.formData.type = 'project'
          this.IsDepartDisabled = true
        }
      },
      calculateDifference() {
        if (this.formData.startTime && this.formData.endTime) {
          const start = new Date(this.formData.startTime)
          const end = new Date(this.formData.endTime)
          const workStartHour = this.workStartHour
          const workEndHour = this.workEndHour
          let totalHours = 0

          // 遍历开始日期和结束日期之间的每一天
          for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
            const currentDayStart = new Date(d)
            currentDayStart.setHours(
              Math.floor(workStartHour),
              (workStartHour % 1) * 60,
              0,
              0
            )

            const currentDayEnd = new Date(d)
            currentDayEnd.setHours(
              Math.floor(workEndHour),
              (workEndHour % 1) * 60,
              0,
              0
            )

            if (d.toDateString() === start.toDateString()) {
              // 如果当前日期是开始日期
              if (start > currentDayStart) {
                currentDayStart.setHours(start.getHours(), start.getMinutes())
              }
            }

            if (d.toDateString() === end.toDateString()) {
              // 如果当前日期是结束日期
              if (end < currentDayEnd) {
                currentDayEnd.setHours(end.getHours(), end.getMinutes())
              }
            }

            if (currentDayStart < currentDayEnd) {
              // 计算当前日期的有效工作时间
              const hours = (currentDayEnd - currentDayStart) / (1000 * 60 * 60)
              totalHours += hours
            }
          }

          // 循环完成后处理总小时数
          const totalMinutes = totalHours * 60
          const displayHours = Math.floor(totalMinutes / 60)
          const displayMinutes = Math.round(totalMinutes % 60) // 进行四舍五入确保分钟是整数

          console.log(`${displayHours}小时${displayMinutes}分钟`)
          this.formData.duration = `${displayHours}小时${displayMinutes}分钟`
          console.log(totalHours)
        }
      },

      formatDateMillisecond(totalHours) {
        const totalMinutes = Math.floor(totalHours / 1000 / 60) // 将毫秒转换为分钟
        const displayHours = Math.floor(totalMinutes / 60) // 获取小时
        const displayMinutes = Math.floor(totalMinutes % 60) // 获取分钟，确保为整数

        const formattedDuration = `${displayHours}小时${displayMinutes}分钟`

        console.log(formattedDuration)
        this.formData.duration = formattedDuration
      },
      getDepartList() {
        getDepartList({}).then((response) => {
          this.departTreeData = response.result
        })
      },
      showUploadItem() {
        const data = {
          bizId: this.formData.id,
          bizCode: 'compensatoryLeavePicture',
          isOneFile: true,
        }
        this.$refs.uploadItemRef.showUploadFileDialog(data)
      },
      callbackSuccess(data) {
        console.log('===========')
        console.log(data.urlPath)
        this.formData.picture = data.urlPath
      },
      setAllCheck() {
        if (this.formData.startTime && this.formData.endTime) {
          var sTime = Date.parse(this.formData.startTime)
          var eTime = Date.parse(this.formData.endTime)
          // 两个时间戳相差的毫秒数
          var usedTime = eTime - sTime
          // 计算相差的天数
          var days = Math.floor(usedTime / (24 * 3600 * 1000))
          // 计算天数后剩余的毫秒数
          var leave1 = usedTime % (24 * 3600 * 1000)
          // 计算出小时数
          var hours = Math.floor(leave1 / (3600 * 1000))
          // 计算剩余的分钟数（如果需要）
          var leave2 = leave1 % (3600 * 1000)
          var minutes = Math.floor(leave2 / (60 * 1000))

          // 格式化输出
          this.formData.duration = days + '天' + hours + '时' + minutes + '分'
        }
      },

      async getDictDetailsByCode() {
        this.selectLeaveType = []
        getDictList({ dictCode: 'leaveType' }).then((response) => {
          this.selectLeaveTypeList = response.result[0].children
          for (const data of this.selectLeaveTypeList) {
            const treeNode = {}
            treeNode.id = data.id
            treeNode.label = data.dictName
            this.selectLeaveType.push(treeNode)
          }
        })
      },

      showUserDialog() {
        this.$refs.UserCopyPopup.showUserDialog(
          this.formData.substituteName,
          this.formData.substitute,
          'compensatoryLeave'
        )
      },
      callBackUser(val, data) {
        this.formData.substitute = val.map((item) => item).join(',')
        this.formData.substituteName = data.map((item) => item).join(',')
      },

      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          isAdd: true,
          picture: '',
          reasons: '', //请假原因
          operationCode: 'TXQJ',
          serialNumberTable: 'leave_application',
          substitute: '', //代班人id
          type: 'project',
          substituteName: '',
          leaveType: '', //请假类型 字典
          startTime: '', //开始时间
          endTime: '', //结束时间
          isProject: 0,
          applicationDate: new Date(), //申请日期
          depart: this.departList[0].id, //项目所属部门id
          departCode: this.departList[0].departCode, //部门code
          createDepart: this.departList[0].id,
          projectId: '', //项目id
          projectName: '', //项目名称
          departList: this.departList,
          duration: '', //时长
          createBy: this.userId, //创建人id
          createByName: this.userName,
          viewUser: this.userId, //创建人姓名
          createTime: new Date(), //创建时间
          updateBy: '', //修改人 修改的时候填写
          updateByName: '',
          updateTime: '', //修改时间
          remarks: '', //备注
          sort: '', //排序
        }
      },

      showEdit(row) {
        if (!row) {
          this.dialogStatus = 'create'
          this.initForm()
        } else {
          this.dialogStatus = 'update'
          this.dialogFormVisible = true
          this.formData = Object.assign({ isAdd: false }, row)
          this.formData.updateBy = this.userName
          if (this.formData.type === 'project') {
            this.formData.isProject = 0
          } else {
            this.formData.isProject = 1
          }
          // this.formData.updateByName = this.userName
          this.formData.updateTime = new Date()
        }
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'compensatoryLeave',
          })
        })
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      async saveData() {
        this.$refs['dataForm'].validate(async (valid) => {
          if (valid) {
            let userList = [].concat(this.formData.createBy)
            let params = {
              startDate: this.formData.startTime,
              endDate: this.formData.endTime,
              id: this.formData.id,
              userList,
            }
            const res = await getAllPersonLeaveRecordTime(params)
            let flag = res.result.length > 0 ? true : false
            console.log(flag, 'flag')
            if (flag) {
              this.$message({
                message: '选择时间段内同行人或申请人有考勤冲突状态!',
                type: 'error',
              })
              return
            }
            saveData(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
