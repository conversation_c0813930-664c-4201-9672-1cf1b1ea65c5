<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="合同详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1650px"
    @closed="closedDialog"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-descriptions
          border
          class="descriptions-box"
          :column="3"
          size="medium"
        >
          <el-descriptions-item>
            <template slot="label">合同单号</template>
            {{ formData.serialNumber }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">登记部门</template>
            {{ formData.createDepartName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">登记日期</template>
            {{ formData.recordDate | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目单号</template>
            {{ formData.projectCode }}
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template slot="label">项目名称</template>
            {{ formData.projectName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">合同编号</template>
            {{ formData.contractCode }}
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template slot="label">合同名称</template>
            {{ formData.contractName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">合同金额(元)</template>
            {{ formData.contractMoney | currencyFormat }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">变更后金额(元)</template>
            {{ formData.contractRealMoney | currencyFormat }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">合同类型</template>
            {{ formData.contractTypeName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">合同签署单位</template>
            {{ formData.clienteleName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">签署时间</template>
            {{ formData.signedTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">合同份数</template>
            {{ formData.contractNum }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目所属部门</template>
            {{ formData.departName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">业务类型</template>
            {{ formData.bizTypeName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">登记人</template>
            {{ formData.createByName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目负责人</template>
            {{ formData.projectPrincipalName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目地址</template>
            {{ formData.siteStart }}
            <el-link
              v-if="formData.siteStart !== ''"
              type="primary"
              @click="showBMap"
            >
              <i class="el-icon-map-location"></i>
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目坐标</template>
            {{ formData.siteStartX }}
            <el-link
              v-if="formData.siteStartX !== ''"
              type="primary"
              @click="showBMap"
            >
              <i class="el-icon-map-location"></i>
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">收款条件</template>
            <!--            {{ formData.collectionTerms }}-->
            <div v-html="$formatText(formData.collectionTerms)"></div>
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">备注</template>
            {{ formData.remarks }}
          </el-descriptions-item>
        </el-descriptions>
        <!-- 附件上传 -->
        <UploadLargeFileFdfsPopupDetail
          ref="UploadLargeFileFdfsPopupDetail"
          style="margin-top: 20px"
        />
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="showApprovalRecords && formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
    <BMapShow ref="BMapShow" />
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  import { getDataAndDetail } from '@/api/contract'
  import { constantsExpose } from '@/utils/constantsExpose.js'
  import BMapShow from '@/views/common/BMap/BMapShow.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
      BMapShow,
    },
    props: {
      projectTypeList: {
        type: Array,
        default() {
          return []
        },
      },
      contractTypeList: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        isApproval: false,
        selectType: '',
        showApprovalRecords: true, //详情状态默认展示审批记录
      }
    },
    methods: {
      // formatText(text) {
      //   if (!text) return ''
      //   return text
      //     .replace(/ /g, '&nbsp;') // 保留空格
      //     .replace(/\n/g, '<br>') // 保留换行
      // },
      // getTextStyle(text) {
      //   return {
      //     textAlign: text ? 'left' : 'center',
      //   }
      // },
      async showApprovalFlowByParams(bizKey, row, paramsMap) {
        await this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(
            bizKey,
            row,
            paramsMap
          )
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      closedDialog() {
        this.formData = {}
        this.isApproval = false
        this.selectType = ''
      },
      async showDialog(row) {
        this.isApproval = false
        const params = {
          id: row.id,
          systemId: constantsExpose.SYSTEM_ID,
          selectType: 'income',
        }
        const { result } = await getDataAndDetail(params)
        this.formData = { ...result }
        this.selectType = this.formData.selectType
        this.dialogDetailVisible = true
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: 'contractManagement_revenueContract',
            isShow: true,
          })
        })
      },
      formateContractType(ids) {
        const contractTypeIds = (!!ids && ids.split(',')) || []
        const contractTypeData = this.contractTypeList.filter((tmp) =>
          contractTypeIds.includes(tmp.value)
        )
        const contractTypeNameData = contractTypeData.map((item) => item.label)
        return contractTypeNameData.join('、')
      },
      formateProjectType(ids) {
        const bizTypeIds = (!!ids && ids.split(',')) || []
        const bizTypeData = this.projectTypeList.filter((tmp) =>
          bizTypeIds.includes(tmp.value)
        )
        const bizTypeNameData = bizTypeData.map((item) => item.label)
        return bizTypeNameData.join('、')
      },
      showBMap() {
        let position = this.formData.siteStartX.split(',')
        let marker = {
          position: {
            lng: parseFloat(position[0]),
            lat: parseFloat(position[1]),
          },
          info:
            this.formData.projectName +
            '<br/>' +
            '项目地址：' +
            this.formData.siteStart,
        }
        this.$refs.BMapShow.showBaiDuMap(marker)
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .descriptions-box {
    .el-descriptions-item__cell {
      min-width: 150px;
    }
  }
</style>
