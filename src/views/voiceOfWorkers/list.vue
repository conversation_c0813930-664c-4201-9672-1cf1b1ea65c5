<template>
  <div class="custom-table-container" :class="{ 'vab-fullscreen': isFullscreen }">
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form ref="form" :inline="true" label-width="80px" :model="queryForm" @submit.native.prevent>
          <el-form-item label="类型" label-width="100px" prop="type">
            <el-select v-model="queryForm.type" filterable clearable placeholder="请选择" style="width: 200px">
              <el-option v-for="item in typeLsit" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" native-type="submit" type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-button icon="el-icon-refresh-right" @click.native="resetFormPage">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button v-permissions="{ role: ['admin'] }" icon="el-icon-document" type="primary"
          @click.native="selectPersonnel">
          配置短信接收人
        </el-button>
        <span>当前短信接收人：{{formData.userName}}</span>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button style="margin: 0 10px 10px 0 !important" type="primary" @click="clickFullScreen">
          <vab-icon :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'" />
          表格全屏
        </el-button>
        <el-popover ref="popover" popper-class="custom-table-checkbox" trigger="hover">
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox :disabled="item.disableCheck === true" :label="item.label">
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button icon="el-icon-setting" style="margin: 0 0 10px 0 !important" type="primary">
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table ref="tableSort" v-loading="listLoading" border :data="list" :height="height" :size="lineHeight" stripe
      @selection-change="setSelectRows">
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in finallyColumns" :key="index"
        :align="item.center ? item.center : 'center'" :label="item.label" :prop="item.prop"
        :show-overflow-tooltip="item.label === '内容' || item.label === '备注'? true : false"
        :sortable="item.sortable ? item.sortable : false" :width="item.width ? item.width : 'auto'">
        <template #default="{ row }">
          <span v-if="item.label === '类型'">
            {{ row.type == '1' ? '好建议' : row.type == '2' ? '金点子' : '急难愁盼要接解决' }}
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="300">
        <template #default="{ row }">
          <el-button icon="el-icon-view" style="margin: 0 10px 10px 0 !important" type="success"
            @click="handleDetail(row)">
            详情
          </el-button>
          <el-button icon="el-icon-delete" style="margin: 0 10px 10px 0 !important" type="danger"
          v-permissions="{ permission: ['voiceOfWorkers:del'] }" @click="handleDelete(row)"> 删除 </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image class="vab-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
      </template>
    </el-table>
    <el-pagination background :current-page="pageInfo.curPage" :layout="layout" :page-size="pageInfo.pageSize"
      :total="pageInfo.total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    <UserListPopup ref="userListPopupRef" @getUserInfo="getUserInfo" :isMulti="true" />
    <tableDetail ref="tableDetail" />
  </div>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import UserListPopup from '@/views/common/UserListPopup.vue'
  import tableDetail from './detail.vue'
  import {
    getDataListByPage,
    deleteData,
    getAlertConfigData,
    saveVoiceOfWorkers
  } from '@/api/wechat/wechat-api.js'

  export default {
    name: 'voiceOfWorkers',
    components: {
      UserListPopup,
      tableDetail
    },
    mixins: [tableMix],
    data() {
      return {
        formMaxHeight: 1,
        list: [],
        formData: {},
        columns: [
          {
            label: '类型',
            prop: 'type',
          },
          {
            label: '姓名',
            prop: 'name',
          },
          {
            label: '单位名称',
            prop: 'departName',
          },
          {
            label: '联系方式',
            prop: 'telephone',
          },
          {
            label: '内容',
            prop: 'content',
          },
          {
            label: '时间',
            prop: 'time',
          },
          {
            label: '备注',
            prop: 'remark',
          },
        ],
        queryForm: {
          type: '',
        },
        typeLsit: [{
          label: '好建议',
          value: '1'
        },
        {
          label: '金点子',
          value: '2'
        }, {
          label: '急难愁盼要解决',
          value: '3'
        }]
      }
    },
    created() {
      this.fetchData()
      this.getAlertConfigData()
    },
    methods: {
      selectPersonnel() {
        let selUserList = []
        const userIdArr = this.formData.content.split(',')
        const userNameArr = this.formData.userName.split(',')
        const telephoneArr = this.formData.userTelephone.split(',')
        userIdArr.forEach((userId, index) => {
          selUserList.push({
            userId,
            userName: userNameArr[index],
            telephone: telephoneArr[index]
          })
        })
        this.$refs.userListPopupRef.showUserDialog({ selUserList })
      },
      getUserInfo(data) {
        console.log(data)
        if (data.length > 0) {
          this.formData.content = data.map(item => item.userId).join(',')
          this.formData.userName = data.map(item => item.userName).join(',')
          this.formData.userTelephone = data.map(item => item.telephone).join(',')
        } else {
          this.$baseMessage('至少选择一位！！', 'error', 'vab-hey-message-error')
          return
        }
        saveVoiceOfWorkers({ ...this.formData, isAdd: false }).then((res) => {
          if (res.code == '200') {
            this.$baseMessage('修改成功!', 'success', 'vab-hey-message-success')
            this.getAlertConfigData()
          } else {
            this.$baseMessage(response.msg, 'error', 'vab-hey-message-error')
          }
        })
      },
      getAlertConfigData() {
        getAlertConfigData({}).then((res) => {
          this.formData = res.result
        })
      },
      fetchData() {
        this.listLoading = true
        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },
      //详情
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      // 删除
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteData({ id: row.id })
              .then(() => {
                this.pageInfo.curPage = 1
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';

  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>