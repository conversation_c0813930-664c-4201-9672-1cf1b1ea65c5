<template>
  <div class="box" v-loading="formLoading">
    <div class="header">
      <!-- <img src="https://minio.jxth.com.cn/files/oa/51995b2e-8e89-4ab6-b087-ff2b26f85c8e.png" /> -->
      {{ title }}
      <span v-if="showForm" class="back" @click="back"><vab-icon icon="arrow-go-back-fill" /></span>
    </div>
    <div class="main">
      <el-form v-if="showForm" ref="dataForm" :rules="rules" :model="formData">
        <el-form-item label="具体内容" prop="content">
          <el-input type="textarea" v-model="formData.content" maxlength="1000" placeholder="请输入具体内容" :rows="5" />
        </el-form-item>
        <el-form-item label="单位名称" prop="departName">
          <el-input type="text" v-model="formData.departName" placeholder="请输入单位名称" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input type="text" v-model="formData.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="联系方式" prop="telephone">
          <el-input type="text" v-model="formData.telephone" placeholder="请输入方式" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="formData.remark" maxlength="1000" placeholder="请输入其他内容" :rows="5" />
        </el-form-item>
        <el-button type="primary" class="bind" @click="submit">提交
        </el-button>
      </el-form>
      <div v-else>
        <el-image @click="changeType('1')"
          src="https://minio.jxth.com.cn/files/hnsz/b6fd7c49-029b-487c-a2ff-88015b490fc2.png"></el-image>
        <el-image @click="changeType('2')" style="margin-top: 15px;"
          src="https://minio.jxth.com.cn/files/hnsz/028f3cb1-99b3-4701-8eac-54798d1cd708.png"></el-image>
        <el-image @click="changeType('3')" style="margin-top: 15px;"
          src="https://minio.jxth.com.cn/files/hnsz/83852bae-7be2-4a8d-a42a-674b9c79474c.png"></el-image>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    saveVoiceOfWorkers
  } from '@/api/wechat/wechat-api.js'
  import { genUUID } from '@/utils/th_utils.js'
  export default {
    name: 'voiceOfWorkers',
    components: {},
    data() {
      return {
        title: '欢迎访问通慧科技集团职工之声',
        formLoading: false,
        formData: {
          isAdd: true,
          id: genUUID(),
          type: '',
          content: '',
          departName: '',
          name: '',
          telephone: '',
          typeName: '',
        },
        rules: {
          content: [
            { required: true, message: '具体内容为必填', trigger: 'blur' }
          ],
        },
        showForm: false,
      }
    },
    mounted() {

    },
    methods: {
      changeType(type) {
        this.showForm = true
        this.formData.type = type
        this.formData.typeName = type == '1' ? '好建议' : type == '2' ? '金点子' : '急难愁盼要解决'
        this.title = type == '1' ? '我有一个‘好建议‘' : type == '2' ? '我来出个’金点子‘' : '急难愁盼要解决'
      },
      back() {
        this.showForm = false
        this.title = '欢迎访问通慧科技集团职工之声'
        this.initForm()
      },
      initForm() {
        this.formData = {
          isAdd: true,
          id: genUUID(),
          type: '',
          typeName: '',
          content: '',
          departName: '',
          name: '',
          telephone: '',
          remark: ''
        }
      },
      submit() {
        this.$refs.dataForm.validate(valid => {
          if (valid) {
            this.formLoading = true
            saveVoiceOfWorkers(this.formData).then(response => {
              if (response.code == '200') {
                this.$baseMessage('提交成功!', 'success', 'vab-hey-message-success')
                this.back()
              } else {
                this.$baseMessage(response.msg, 'error', 'vab-hey-message-error')
              }
              this.formLoading = false
            })
          }
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
  .header {
    background: linear-gradient(45deg, #1e93ff, #1e7dff);
    color: #fff;
    font-size: 20px;
    padding: 15px 0px;
    text-align: center;
    position: relative;
    /* padding-top: 80px;
    padding-bottom: 30px; */

    /* img {
      width: 100px;
    } */
    .back {
      position: absolute;
      top: 5px;
      left: 10px;
      font-size: 20px;
      display: inline-block;
      width: 40px;
      height: 40px;
      line-height: 40px;
    }
  }

  .box {
    height: 100vh;

    .main {
      margin-top: 22px;
      display: flex;
      flex-direction: column;
      padding-left: 24px;
      padding-right: 24px;

      .bind {
        width: 100%;
        background: linear-gradient(to right, rgb(0, 82, 212), rgb(67, 100, 247), rgb(111, 177, 252));
        color: rgb(255, 255, 255);
        height: 55px;
        font-size: 16px;
        border-radius: 2.5rem;
        margin-top: 25px;
      }
    }

  }
</style>
<!-- <style lang="scss">
  .box .main .el-input__inner {
    padding: 17px;
    height: auto;
    line-height: normal;
    border-radius: 30px;
  }

  .box .main .el-input .el-input__clear {
    font-size: 20px;
    margin-right: 10px;
  }
</style> -->