<template>
  <div class="add-node-btn-box">
    <div class="add-node-btn">
      <el-popover placement="right-start" v-model="visible" v-if="isAddProcessDesign">
        <div class="add-node-popover-body">
          <a class="add-node-popover-item approver" @click="addType('userTask')">
            <div class="item-wrapper">
              <i class="el-icon-s-check"></i>
            </div>
            <p>审批人</p>
          </a>
          <a class="add-node-popover-item condition" @click="addType('exclusiveGateway')">
            <div class="item-wrapper">
              <i class="el-icon-share"></i>
            </div>
            <p>条件分支</p>
          </a>
          <!-- <a class="add-node-popover-item concurrent" @click="addType('parallelGateway')">
            <div class="item-wrapper">
              <i class="el-icon-s-operation"></i>
            </div>
            <p>并行分支</p>
          </a> -->
        </div>
        <button class="btn" type="button" slot="reference">
          <span class="iconfont"></span>
        </button>
      </el-popover>
    </div>
  </div>
</template>
<script>
  import { mapGetters } from 'vuex'
  import { deepClone } from '@/utils'
  import { getRandomId } from '../common.js'
  import DefaultProps from "../data.js"
  export default {
    props: ["childNodeP", "id"],
    data() {
      return {
        visible: false
      }
    },
    computed: {
      ...mapGetters({
        isAddProcessDesign: 'workFlow/isAddProcessDesign'
      })
    },
    methods: {
      addType(type) {
        this.visible = false;
        let data = {
          id: getRandomId(type),
          parentId: this.id,
          type: type,
          dueDay: 0,
          children: {}
        }
        if(type === 'userTask') {
          data = {
            ...data,
            name: '审批人',
            showError: false,
            sendCopyUserIds: '',
            sendCopyUserNames: '',
            assignee: '',
            assigneeName: '',
            listenerIds: '',
            listenerNames: '',
            approvalType: 1,
          }
          if(this.childNodeP.type === 'EMPTY') {
            data.children = this.childNodeP.children
            data.parentId = this.childNodeP.id
            if(JSON.stringify(data.children) !== '{}') {
              data.children.parentId = data.id
            }
            this.childNodeP.children = data
            this.$emit("update:childNodeP", this.childNodeP)
          } else {
            if(JSON.stringify(this.childNodeP) !== '{}') {
              this.childNodeP.parentId = data.id
            }
            data.children = this.childNodeP
            this.$emit("update:childNodeP", data)
          }
        } else if(type === 'exclusiveGateway') {
          data.name = '条件分支'
          data.children = {
            id: getRandomId('empty'),
            parentId: data.id,
            parentType: data.type,
            type: "EMPTY",
            children: {}
          }
          data.branchs = [
            {
              id: getRandomId('condition'),
              parentId: data.id,
              type: "condition",
              name: "条件分支1",
              children: {},
              showError: false,
              condition: "",
              conditionName: "",
            },{
              id: getRandomId('condition'),
              parentId: data.id,
              type: "condition",
              name: "条件分支2",
              children: {},
              showError: false,
              condition: "",
              conditionName: "",
            }
          ]
        } else if(type === 'parallelGateway') {
          data.name = '并行分支'
          data.children = {
            id: getRandomId('empty'),
            parentId: data.id,
            type: "EMPTY",
            children: {}
          }
          data.branchs = [
            {
              id: getRandomId('branch'),
              parentId: data.id,
              type: "branch",
              name: "并行分支1",
              children: {}
            },{
              id: getRandomId('branch'),
              parentId: data.id,
              type: "branch",
              name: "并行分支2",
              children: {}
            }
          ]
        }
        if(type === 'exclusiveGateway' || type === 'parallelGateway') {
          if(this.childNodeP.type === 'EMPTY') {
            data.children = this.childNodeP.children
            data.parentId = this.childNodeP.id
            if(JSON.stringify(data.children) !== '{}') {
              data.children.parentId = data.id
            }
            this.childNodeP.children = data
            this.$emit("update:childNodeP", this.childNodeP)
          } else {
            if(JSON.stringify(this.childNodeP) !== '{}') {
              this.childNodeP.parentId = data.children.id
            }
            data.children.children = this.childNodeP
            this.$emit("update:childNodeP", data)
          }
        }
        this.$forceUpdate()
      }
    }
  }
</script>
<style scoped lang="scss">
  .add-node-btn-box {
    width: 240px;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    position: relative;

    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      margin: auto;
      width: 2px;
      height: 100%;
      background-color: #cacaca
    }

    .add-node-btn {
      user-select: none;
      width: 240px;
      padding: 20px 0 40px;
      display: flex;
      -webkit-box-pack: center;
      justify-content: center;
      flex-shrink: 0;
      -webkit-box-flex: 1;
      flex-grow: 1;

      .btn {
        outline: none;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .1);
        width: 30px;
        height: 30px;
        background: #3296fa;
        border-radius: 50%;
        position: relative;
        border: none;
        line-height: 30px;
        -webkit-transition: all .3s cubic-bezier(.645, .045, .355, 1);
        transition: all .3s cubic-bezier(.645, .045, .355, 1);

        .iconfont {
          color: #fff;
          font-size: 16px
        }

        &:hover {
          transform: scale(1.3);
          box-shadow: 0 13px 20px 0 rgba(0, 0, 0, .1)
        }

        &:active {
          transform: none;
          background: #1e83e9;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .1)
        }
      }
    }
  }
</style>
<style lang="scss">
  .add-node-popover-body {
    display: flex;

    .add-node-popover-item {
      margin-right: 10px;
      cursor: pointer;
      text-align: center;
      flex: 1;
      color: #191f25 !important;

      .item-wrapper {
        user-select: none;
        display: inline-block;
        width: 60px;
        height: 60px;
        margin-bottom: 5px;
        background: #fff;
        border: 1px solid #e2e2e2;
        border-radius: 50%;
        transition: all .3s cubic-bezier(.645, .045, .355, 1);

        .iconfont {
          font-size: 24px;
          line-height: 60px
        }

        i {
          font-size: 24px;
          line-height: 60px
        }
      }

      &.approver {
        .item-wrapper {
          color: #ff943e
        }
      }

      &.notifier {
        .item-wrapper {
          color: #3296fa
        }
      }

      &.condition {
        .item-wrapper {
          color: #15bc83
        }
      }

      &.concurrent {
        .item-wrapper {
          color: #aa7f13
        }
      }

      &:hover {
        .item-wrapper {
          background: #3296fa;
          box-shadow: 0 10px 20px 0 rgba(50, 150, 250, .4)
        }

        .iconfont {
          color: #fff
        }

        i {
          color: #fff
        }
      }

      &:active {
        .item-wrapper {
          box-shadow: none;
          background: #eaeaea
        }

        .iconfont {
          color: inherit
        }

        i {
          color: inherit
        }
      }
    }
  }
</style>
