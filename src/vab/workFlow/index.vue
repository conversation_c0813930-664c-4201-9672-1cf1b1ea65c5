<template>
  <el-dialog
    append-to-body
    :visible.sync="dialogFormVisible"
    :fullscreen="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    custom-class="workflow-dialog"
    :destroy-on-close="true"
    @close="closeDialog"
  >
    <div slot="title">
      <div class="fd-nav">
        <div class="fd-nav-left">
          <div class="fd-nav-back" @click="toReturn">
            <i class="anticon anticon-left"></i>
          </div>
          <div class="fd-nav-title">{{ name }}</div>
        </div>
        <div class="fd-nav-right" v-if="isAddProcessDesign">
          <button
            type="button"
            class="ant-btn button-publish"
            @click="overAllConfig"
          >
            <span>全局配置</span>
          </button>
          <button type="button" class="ant-btn reset-btn" @click="resetConfig">
            <span>重置</span>
          </button>
          <button type="button" class="ant-btn check-btn" @click="check">
            <span>合法校验</span>
          </button>
          <button type="button" class="ant-btn button-publish" @click="saveSet">
            <span>发 布</span>
          </button>
        </div>
      </div>
    </div>
    <div class="fd-nav-content">
      <section class="dingflow-design">
        <div class="zoom">
          <div
            :class="'zoom-out' + (nowVal == 50 ? ' disabled' : '')"
            @click="zoomSize(1)"
          ></div>
          <span>{{ nowVal }}%</span>
          <div
            :class="'zoom-in' + (nowVal == 300 ? ' disabled' : '')"
            @click="zoomSize(2)"
          ></div>
        </div>
        <div
          class="box-scale"
          id="box-scale"
          :style="
            'transform: scale(' +
            nowVal / 100 +
            '); transform-origin: 50% 0px 0px;'
          "
        >
          <nodeWrap
            v-if="!!nodeConfig && !isEmptyObj(nodeConfig)"
            :nodeConfig.sync="nodeConfig"
            :roleList="roleList"
            :departList="departList"
          ></nodeWrap>
        </div>
      </section>
    </div>
    <!-- 提示 -->
    <ErrorDialog :visible.sync="tipVisible" :list="tipList" />
    <NodeConfigDrawer
      ref="nodeConfigDrawerRef"
      :noShowCondition="noShowCondition"
    />
    <!-- 全局配置 -->
    <OverAllConfigDialog ref="overAllConfigRef" @callback="getOverAllConfig" />
  </el-dialog>
</template>

<script>
  import { mapMutations, mapGetters } from 'vuex'
  import { deepClone } from '@/utils'
  import ErrorDialog from './components/errorDialog'
  import NodeConfigDrawer from './components/nodeConfigDrawer.vue'
  import DefaultProps from './data'
  import {
    isPrimaryNode,
    isBranchNode,
    isBranchSubNode,
    isEmptyObj,
    validApprovalNode,
    validConditionNode,
    getRandomId,
  } from './common.js'
  import { deployWorkFlow, checkWorkFlow } from '@/api/workflow/workflow-api'
  import { getDepartList } from '@/api/system/depart-api'
  import { getRoleList } from '@/api/system/role-api'
  import OverAllConfigDialog from './components/overAllConfigDialog.vue'
  export default {
    name: 'ProcessDefineConfig',
    components: {
      ErrorDialog,
      NodeConfigDrawer,
      OverAllConfigDialog,
    },
    data() {
      return {
        dialogFormVisible: false,
        name: '',
        tipList: [],
        tipVisible: false,
        nowVal: 100,
        nodeConfig: null,
        flowDefineInfo: null,
        departList: [],
        roleList: [],
        noShowCondition: [], // 特别处理不需要的条件
      }
    },
    computed: {
      ...mapGetters({
        isAddProcessDesign: 'workFlow/isAddProcessDesign',
      }),
    },
    beforeDestroy() {
      this.nodeConfig = null
      this.flowDefineInfo = null
    },
    async created() {
      this.getDepartList()
      this.getRoleList()
    },
    methods: {
      ...mapMutations({
        setIsTried: 'workFlow/setIsTried',
        setIsAddProcessDesign: 'workFlow/setIsAddProcessDesign',
      }),
      closeDialog() {
        this.dialogFormVisible = false
        this.nodeConfig = null
        this.flowDefineInfo = null
      },
      showDialog(data, noShowCondition = []) {
        this.noShowCondition = noShowCondition
        this.flowDefineInfo = { ...data }
        this.name = data.name + '-流程'
        if (!!data.originProcessData) {
          this.nodeConfig = JSON.parse(data.originProcessData)
        } else {
          this.nodeConfig = deepClone(DefaultProps.PROCESS)
        }
        this.dialogFormVisible = true
      },
      toReturn() {
        if (this.isAddProcessDesign) {
          this.$baseConfirm(
            '确认退出当前流程设计吗？',
            '',
            () => {
              this.dialogFormVisible = false
            },
            () => {}
          )
          return
        }
        this.dialogFormVisible = false
      },
      zoomSize(type) {
        if (type == 1) {
          if (this.nowVal == 50) return
          this.nowVal -= 10
        } else {
          if (this.nowVal == 300) return
          this.nowVal += 10
        }
      },
      isEmptyObj(node) {
        return isEmptyObj(node)
      },
      async check() {
        const result = this.commonValidateFlowData()
        if (!result) return
        const { nodeList, lineListData } = result
        //发起请求 校验是否合法
        const paramsData = {
          ...this.flowDefineInfo,
          nodeList,
          lineList: lineListData,
        }
        delete paramsData.systemId
        await checkWorkFlow(paramsData)
          .then((res) => {
            this.$baseMessage('校验成功', 'success', 'vab-hey-message-success')
          })
          .catch((res) => {
            this.$baseMessage(res.msg, 'error', 'vab-hey-message-error')
          })
      },
      saveSet() {
        const result = this.commonValidateFlowData()
        if (!result) return
        const { nodeList, lineListData } = result
        const paramsData = {
          ...this.flowDefineInfo,
          nodeList,
          lineList: lineListData,
          tenantId: this.flowDefineInfo.systemId,
          originProcessData: JSON.stringify(deepClone(this.nodeConfig)),
        }
        delete paramsData.systemId
        this.$confirm(
          '是否部署新的的流程，原来的节点配置将重置，已经启动的流程节点配置无法改变?',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(async () => {
          deployWorkFlow(paramsData)
            .then((response) => {
              this.$baseMessage(
                '发布成功!',
                'success',
                'vab-hey-message-success'
              )
              this.$emit('refresh')
              this.closeDialog()
            })
            .catch((err) => {
              this.$baseMessage(err.msg, 'error', 'vab-hey-message-error')
            })
        })
      },
      commonValidateFlowData() {
        this.setIsTried(true)
        this.tipList = this.validateProcess()
        if (this.tipList.length != 0) {
          this.tipVisible = true
          return
        }
        const { nodeList, lineListData } = this.getFlowData()
        if (nodeList.length <= 2) {
          this.$notify({
            title: '提示',
            message: '请完善流程设计!',
            type: 'warning',
            duration: 2000,
          })
          return
        }
        return { nodeList, lineListData }
      },
      getFlowData() {
        let nodeList = []
        let lineList = []
        this.getNodeData(this.nodeConfig, nodeList)
        const endNode = nodeList.find(item => item.type === 'endEvent')
        const endId = endNode.id
        this.getLineData(this.nodeConfig, lineList, endId, '')
        let lineListData = []
        lineList.reduce((cur, next) => {
          if (!cur[next.from + next.to]) {
            lineListData.push(next)
            cur[next.from + next.to] = '1'
          }
          return cur
        }, {})
        return { nodeList, lineListData }
      },
      getNodeData(node, nodeList) {
        if (!node || JSON.stringify(node) === '{}') return
        let nodeData = deepClone(node)
        delete nodeData.children
        delete nodeData.branchs
        delete nodeData.showError
        delete nodeData.parentId
        if(node.type === 'EMPTY') {
          this.getNodeData(node.children, nodeList)
        } else {
          nodeList.push(nodeData)
          if (node.branchs && node.branchs.length > 0) {
            for (let i = 0; i < node.branchs.length; i++) {
              this.getNodeData(node.branchs[i].children, nodeList)
            }
          }
          this.getNodeData(node.children, nodeList)
        }
      },
      getLineData(node, lineList, endId, toId = '') {
        if (!node || !node.children || isEmptyObj(node)) return
        if (node.branchs && node.branchs.length > 0) {
          if (node.type === 'exclusiveGateway') {
            for (let i = 0; i < node.branchs.length; i++) {
              if (isEmptyObj(node.branchs[i].children)) {
                lineList.push({
                  id: getRandomId('line'),
                  from: node.id,
                  to: node.children.children.id,
                  conditionExpression: node.branchs[i].condition || '',
                })
              } else {
                lineList.push({
                  id: getRandomId('line'),
                  from: node.id,
                  to: node.branchs[i].children.id,
                  conditionExpression: node.branchs[i].condition || '',
                })
                const toIds = node.children.children.id + ',' + toId
                this.getLineData(node.branchs[i].children, lineList, endId, toIds)
              }
            }
            const toIds = toId
            this.getLineData(node.children.children, lineList, endId, toIds)
          }
        } else {
          if (node.type !== 'endEvent' && isEmptyObj(node.children)) {
            const id = toId.split(',')[0]
            lineList.push({
              id: getRandomId('line'),
              from: node.id,
              to: id || endId,
            })
          } else {
            if (
              !(
                !node.children ||
                isEmptyObj(node.children) ||
                node.children.type === 'parallelGateway'
              )
            ) {
              lineList.push({
                id: getRandomId('line'),
                from: node.id,
                to: node.children.id,
              })
              let toIds = toId
              if(!node.children.children || isEmptyObj(node.children.children)) {
                toIds = toIds.split(',')
                toIds = toIds.length > 2 ? toIds.splice(1).join(',') : toIds.join(',')
              }
              this.getLineData(node.children, lineList, endId, toIds)
            }
          }
        }
      },
      validateProcess() {
        let err = []
        this.validate(err, this.nodeConfig)
        return err
      },
      validate(err, node) {
        if (isPrimaryNode(node)) {
          // 校验业务节点(审核节点、抄送节点)
          this.validateNode(err, node)
          this.validate(err, node.children)
        } else if (isBranchNode(node)) {
          let unHasChild = 0
          node.branchs.forEach((branchNode) => {
            //校验条件节点
            this.validateNode(err, branchNode)
            //校验条件节点后面的节点
            this.validate(err, branchNode.children)
            if (
              node.type === 'parallelGateway' &&
              branchNode.children.type !== 'userTask'
            ) {
              err.push(`${branchNode.name} 结束后未设置审批节点`)
            }
            // if(node.type === 'exclusiveGateway' && isEmptyObj(branchNode.children)) {
            //   unHasChild++;
            // }
          })
          this.validate(err, node.children.children)
          // if(node.type === 'exclusiveGateway' && unHasChild > 0) {
          //   err.push(`条件分支 请完善条件节点设置`)
          // }
        }
      },
      validateNode(err, node) {
        if (node.type === 'startEvent' || node.type === 'endEvent') return
        if (node.type === 'userTask') {
          node.showError = validApprovalNode(err, node)
        } else if (node.type === 'condition') {
          node.showError = validConditionNode(err, node)
        }
      },
      resetConfig() {
        if (!!this.flowDefineInfo.originProcessData) {
          this.nodeConfig = JSON.parse(this.flowDefineInfo.originProcessData)
        } else {
          this.nodeConfig = deepClone(DefaultProps.PROCESS)
        }
      },
      overAllConfig() {
        const {
          sendCopyUserIds,
          sendCopyUserNames,
          listenerIds,
          listenerNames,
          sendCopyType = 1,
        } = this.flowDefineInfo
        const data = {
          sendCopyUserIds,
          sendCopyUserNames,
          listenerIds,
          listenerNames,
          sendCopyType,
        }
        this.$refs.overAllConfigRef.showDialog(data)
      },
      getOverAllConfig(data) {
        this.flowDefineInfo = {
          ...this.flowDefineInfo,
          ...data,
        }
      },
      async getDepartList() {
        const { result } = await getDepartList({})
        this.departList = result.map((item) => {
          return {
            id: item.id,
            label: item.departName,
            departCode: item.departCode,
          }
        })
      },
      async getRoleList() {
        const { result } = await getRoleList({})
        this.roleList = result.map((item) => {
          return {
            id: item.id,
            label: item.roleName,
          }
        })
      },
    },
  }
</script>

<style scoped>
  @import './css/workflow.css';

  .error-modal-list {
    width: 455px;
  }
  .fd-nav-right .check-btn {
    min-width: 80px;
    margin-right: 15px;
    margin-left: 4px;
    color: #ff4949;
    border-color: #ffeded;
  }
  .fd-nav-right .reset-btn {
    min-width: 80px;
    margin-right: 15px;
    margin-left: 4px;
    color: #ff4949;
    border-color: #9aff71;
  }
</style>
