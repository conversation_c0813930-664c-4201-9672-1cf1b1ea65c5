<template>
  <span style="display: inline-block;font-size: 16px;margin-left: 10px;">
    <el-popover
      placement="bottom"
      trigger="hover"
    >
      <div>
        <!-- <vue-qr ref="downQr" :logo-src="logo" :text="baseURL + '/#/downloadApk'" :size="150" :margin="2" /> -->
        <!-- <div style="text-align: center;">请用手机扫描二维码</div> -->
        <el-image style="width: 200px; height: 200px" src="https://minio.jxth.com.cn/files/oa/dd72b072-449b-41de-98bc-32e4b726fc11.png"></el-image>
        <el-image style="width: 200px; height: 200px" src="https://minio.jxth.com.cn/files/oa/b154757f-8220-4849-aced-daa7c83631ee.jpg"></el-image>
      </div>
      <el-link slot="reference" type="success"><i class="el-icon-mobile-phone" /></el-link>
    </el-popover>
  </span>
</template>

<script>
  import VueQr from 'vue-qr'
  import { baseURL } from '@/config'
  export default {
    name: 'VabAppDownload',
    components: {
      VueQr
    },
    data() {
      return {
        baseURL,
        logo: require('@/assets/appDownload/logo.png')
      }
    }
  }
</script>

<style lang="scss" scoped>
</style>
